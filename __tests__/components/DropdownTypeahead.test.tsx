import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DropdownTypeahead, {
  Option,
} from '../../src/components/DropdownTypeahead';

describe('DropdownTypeahead', () => {
  const mockOptions: Option[] = [
    {value: '1', label: 'Option 1'},
    {value: '2', label: 'Option 2'},
    {value: '3', label: 'Option 3'},
  ];

  const defaultProps = {
    label: 'Test Dropdown',
    options: mockOptions,
    selected: null,
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with proper accessibility attributes', () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');
    expect(input).toHaveAttribute('aria-autocomplete', 'both');
  });

  it('hides label when hideLabel prop is true', () => {
    render(<DropdownTypeahead {...defaultProps} hideLabel={true} />);
    const label = screen.getByText('Test Dropdown');
    expect(label).toHaveStyle({display: 'none'});
  });

  it('disables the input when disabled prop is true', () => {
    render(<DropdownTypeahead {...defaultProps} disabled={true} />);

    const input = screen.getByRole('combobox');
    expect(input).toBeDisabled();
  });

  it('shows error message when isInvalid is true', () => {
    const errorMessage = 'Custom error message';
    render(
      <DropdownTypeahead
        {...defaultProps}
        isInvalid={true}
        errorMessage={errorMessage}
      />,
    );

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('handles single selection correctly', async () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const option = await screen.findByText('Option 1');
    fireEvent.click(option);

    expect(defaultProps.onChange).toHaveBeenCalledWith({
      value: '1',
      label: 'Option 1',
    });
  });

  it('handles multiple selection correctly', async () => {
    const onChange = jest.fn();
    render(
      <DropdownTypeahead
        {...defaultProps}
        multiple={true}
        onChange={onChange}
        selected={[]}
      />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const option1 = await screen.findByText('Option 1');
    fireEvent.click(option1);

    expect(onChange).toHaveBeenCalledWith([
      {
        value: '1',
        label: 'Option 1',
      },
    ]);
  });

  it('calls onInputChange when typing in the input', async () => {
    const onInputChange = jest.fn();
    render(
      <DropdownTypeahead {...defaultProps} onInputChange={onInputChange} />,
    );

    const input = screen.getByRole('combobox');
    await userEvent.type(input, 't');

    // Only check the first argument since the second is a React synthetic event
    expect(onInputChange.mock.calls[0][0]).toBe('t');
  });

  it('displays "No results found" when no options match search', async () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');
    await userEvent.type(input, 'nonexistent');

    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('handles special option selection', async () => {
    const onSpecialOptionSelect = jest.fn();
    render(
      <DropdownTypeahead
        {...defaultProps}
        specialOptionLabel="Add New Option"
        onSpecialOptionSelect={onSpecialOptionSelect}
      />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const specialOption = await screen.findByText('Add New Option');
    fireEvent.click(specialOption);

    expect(onSpecialOptionSelect).toHaveBeenCalled();
  });

  it('displays token with more count for multiple selection', () => {
    const selected = [
      {value: '1', label: 'Option 1'},
      {value: '2', label: 'Option 2'},
      {value: '3', label: 'Option 3'},
      {value: '4', label: 'Option 4'},
      {value: '5', label: 'Option 5'},
    ];

    const {container} = render(
      <DropdownTypeahead
        {...defaultProps}
        multiple={true}
        selected={selected}
      />,
    );

    expect(
      screen.getByText('Option 1, Option 2, Option 3, Option 4'),
    ).toBeInTheDocument();

    const element = container.getElementsByClassName('rbt-token-more')[0];
    expect(element).toHaveTextContent('+1 More');
  });

  it('clears selection when clear button is clicked', async () => {
    const onChange = jest.fn();
    const selected = {value: '1', label: 'Option 1'};
    render(
      <DropdownTypeahead
        {...defaultProps}
        selected={selected}
        onChange={onChange}
      />,
    );

    const clearButton = screen
      .getByTestId('clear-icon')
      .querySelector('button');
    fireEvent.click(clearButton!);

    expect(onChange).toHaveBeenCalledWith(null);
  });

  it('toggles dropdown when clear icon is clicked with no selection', () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const clearButton = screen
      .getByTestId('clear-icon')
      .querySelector('button');
    fireEvent.click(clearButton!);

    expect(screen.getByTestId('clear-icon')).toBeInTheDocument();
  });

  it('renders custom placeholder when placeholder is given', () => {
    render(<DropdownTypeahead {...defaultProps} placeholder={'Test Dropdown'} />);

    const input = screen.getByRole('combobox');
    expect(input).toHaveAttribute('placeholder', 'Test Dropdown');
  });

  it('shows required indicator when required prop is true', () => {
    render(<DropdownTypeahead {...defaultProps} required={true} />);

    const labelElement = screen.getByText('Test Dropdown*');
    expect(labelElement).toBeInTheDocument();
  });

  it('handles non-object options correctly', async () => {
    const stringOptions = ['Option 1', 'Option 2', 'Option 3'];
    render(
      <DropdownTypeahead {...defaultProps} options={stringOptions as any[]} />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const option = await screen.findByText('Option 1');
    expect(option).toBeInTheDocument();
  });

  it('shows danger icon when invalid and multiple with no selection', () => {
    render(
      <DropdownTypeahead
        {...defaultProps}
        multiple={true}
        isInvalid={true}
        selected={[]}
      />,
    );

    const dangerIcon = screen.getByTestId('danger-icon');
    expect(dangerIcon).toBeInTheDocument();
  });

  it('shows cross icon when there is a selected value', () => {
    const selected = {value: '1', label: 'Option 1'};
    render(<DropdownTypeahead {...defaultProps} selected={selected} />);

    const crossIcon = screen.getByTestId('cross-icon');
    expect(crossIcon).toBeInTheDocument();
  });

  it('handles keyboard navigation on wrapper', () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const wrapper = document.querySelector('.typeahead-wrapper');
    expect(wrapper).toBeInTheDocument();

    // Test Enter key
    fireEvent.keyDown(wrapper!, {key: 'Enter'});
    expect(wrapper).toBeInTheDocument();

    // Test Space key
    fireEvent.keyDown(wrapper!, {key: ' '});
    expect(wrapper).toBeInTheDocument();
  });

  it('handles onBlur callback when menu closes after user interaction', async () => {
    const onBlur = jest.fn();
    render(<DropdownTypeahead {...defaultProps} onBlur={onBlur} />);

    const input = screen.getByRole('combobox');

    // Simulate user interaction by clicking on the wrapper first
    const wrapper = document.querySelector('.typeahead-wrapper');
    fireEvent.click(wrapper!);

    // Then focus and blur the input
    fireEvent.focus(input);
    fireEvent.blur(input);

    // Wait for the onBlur to be called
    await waitFor(() => {
      expect(onBlur).toHaveBeenCalled();
    });
  });

  it('handles options with different key properties', async () => {
    const optionsWithId = [
      {id: 'id1', label: 'Option with ID'},
      {value: 'val1', label: 'Option with Value'},
      {label: 'Option with Label only'},
    ];

    render(<DropdownTypeahead {...defaultProps} options={optionsWithId} />);

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const optionWithId = await screen.findByText('Option with ID');
    const optionWithValue = await screen.findByText('Option with Value');
    const optionWithLabel = await screen.findByText('Option with Label only');

    expect(optionWithId).toBeInTheDocument();
    expect(optionWithValue).toBeInTheDocument();
    expect(optionWithLabel).toBeInTheDocument();
  });

  it('handles tooltip titles for options', async () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const optionElement = await screen.findByText('Option 1');
    const tooltipDiv = optionElement.closest('.typehead-option-tooltip');

    expect(tooltipDiv).toHaveAttribute('title', 'Option 1');
  });

  it('handles menu toggle state correctly', async () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');

    // Initially menu should be closed (chevron down)
    const clearButton = screen
      .getByTestId('clear-icon')
      .querySelector('button');
    fireEvent.click(clearButton!);

    // Menu should toggle
    expect(clearButton).toBeInTheDocument();
  });

  it('handles input focus and keydown events', async () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');

    // Test focus event - just verify the input exists and can receive focus
    fireEvent.focus(input);
    expect(input).toBeInTheDocument();

    // Test keydown event
    fireEvent.keyDown(input, {key: 'a'});
    expect(input).toBeInTheDocument();
  });

  it('renders with correct CSS classes for invalid state', () => {
    render(
      <DropdownTypeahead {...defaultProps} isInvalid={true} multiple={true} />,
    );

    const wrapper = document.querySelector('.typeahead-wrapper');
    expect(wrapper).toHaveClass('is-invalid');
    expect(wrapper).toHaveClass('multiple-selection');
  });

  it('handles empty option list with special option', async () => {
    const onSpecialOptionSelect = jest.fn();
    render(
      <DropdownTypeahead
        {...defaultProps}
        options={[]}
        specialOptionLabel="Add New Item"
        onSpecialOptionSelect={onSpecialOptionSelect}
      />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    // Should show "No results found" and special option
    expect(screen.getByText('No results found')).toBeInTheDocument();

    const specialOption = await screen.findByText('Add New Item');
    fireEvent.click(specialOption);

    expect(onSpecialOptionSelect).toHaveBeenCalled();
  });

  it('handles keyboard navigation with Enter and Space keys', async () => {
    const user = userEvent.setup();
    render(<DropdownTypeahead {...defaultProps} />);

    const wrapper = document.querySelector('.typeahead-wrapper');
    expect(wrapper).toBeInTheDocument();

    // Test Enter key
    fireEvent.keyDown(wrapper!, {key: 'Enter'});
    expect(wrapper).toBeInTheDocument();

    // Test Space key
    fireEvent.keyDown(wrapper!, {key: ' '});
    expect(wrapper).toBeInTheDocument();
  });

  it('handles onBlur callback prop', () => {
    const onBlur = jest.fn();
    render(<DropdownTypeahead {...defaultProps} onBlur={onBlur} />);

    // Just test that the onBlur prop is passed correctly
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('handles useCheckboxes with multiple selection correctly', () => {
    const onChange = jest.fn();
    render(
      <DropdownTypeahead
        {...defaultProps}
        multiple={true}
        useCheckboxes={true}
        onChange={onChange}
      />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    // With useCheckboxes, onChange should not be called directly from Typeahead
    // The selection is handled manually through checkboxes
    expect(onChange).not.toHaveBeenCalled();
  });

  it('handles placeholder prop correctly', () => {
    render(<DropdownTypeahead {...defaultProps} placeholder={'Test Dropdown'} />);

    const input = screen.getByRole('combobox');
    expect(input).toHaveAttribute('placeholder', 'Test Dropdown');
  });

  it('handles onInputChange callback', async () => {
    const onInputChange = jest.fn();
    const user = userEvent.setup();

    render(
      <DropdownTypeahead {...defaultProps} onInputChange={onInputChange} />,
    );

    const input = screen.getByRole('combobox');
    await user.type(input, 'test');

    expect(onInputChange).toHaveBeenCalled();
  });

  it('renders with correct styling when invalid and multiple', () => {
    render(
      <DropdownTypeahead {...defaultProps} isInvalid={true} multiple={true} />,
    );

    const wrapper = document.querySelector('.typeahead-wrapper');
    expect(wrapper).toHaveStyle({
      border: '1px solid #dc3545',
      borderRadius: '4px',
      backgroundColor: '#fff',
    });
  });

  it('handles filterBy function when useCheckboxes is true', () => {
    render(
      <DropdownTypeahead
        {...defaultProps}
        multiple={true}
        useCheckboxes={true}
      />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);

    // With useCheckboxes, all options should be visible regardless of filter
    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
    expect(screen.getByText('Option 3')).toBeInTheDocument();
  });
});
