import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import InitialRiskRatingModal, {
  consequenceRows,
  likelihoodCols,
  getCellColor,
} from '../../src/components/InitialRiskRatingModal';

// Import the internal functions for testing by accessing them through the module
// We'll test the compareRiskCodes function indirectly through the disabled functionality

describe('InitialRiskRatingModal', () => {
  const onHideMock = jest.fn();
  const onSelectMock = jest.fn();

  beforeEach(() => {
    onHideMock.mockClear();
    onSelectMock.mockClear();
  });


  it('does not render modal when `show` is false', () => {
    const {container} = render(
      <InitialRiskRatingModal show={false} onHide={onHideMock} />,
    );
    expect(container.querySelector('.modal')).toBeNull();
  });

  it('calls onHide when Cancel button is clicked', () => {
    render(<InitialRiskRatingModal show={true} onHide={onHideMock} />);
    const cancelButton = screen.getByRole('button', {name: /Cancel/i});
    fireEvent.click(cancelButton);
    expect(onHideMock).toHaveBeenCalledTimes(1);
  });

  it('calls onSelect when a tile is clicked', () => {
    render(
      <InitialRiskRatingModal
        show={true}
        onHide={onHideMock}
        onSelect={onSelectMock}
      />,
    );
    const codeToClick = consequenceRows[0].codes[0]; // e.g., 'A1'
    const tile = screen.getByText(codeToClick);
    fireEvent.click(tile);
    expect(onSelectMock).toHaveBeenCalledWith(codeToClick);
  });

  it('highlights selected tile based on selectedValue', () => {
    const selectedCode = consequenceRows[2].codes[3]; // e.g., 'C4'
    render(
      <InitialRiskRatingModal
        show={true}
        onHide={onHideMock}
        selectedValue={selectedCode}
      />,
    );
    const selectedTile = screen.getByText(selectedCode);
    expect(selectedTile).toHaveStyle('border: 2px solid');
  });

  it('renders modal with custom title', () => {
    const customTitle = 'Custom Risk Rating Title';
    render(
      <InitialRiskRatingModal
        show={true}
        onHide={onHideMock}
        title={customTitle}
      />,
    );
    expect(screen.getByText(customTitle)).toBeInTheDocument();
  });

  it('renders modal without title when title prop is not provided', () => {
    render(<InitialRiskRatingModal show={true} onHide={onHideMock} />);
    // Modal should render without crashing when no title is provided
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    // The modal should still have the Cancel button
    expect(screen.getByRole('button', {name: /Cancel/i})).toBeInTheDocument();
  });

  it('renders all consequence rows and likelihood columns', () => {
    render(<InitialRiskRatingModal show={true} onHide={onHideMock} />);

    // Check consequence rows
    consequenceRows.forEach(row => {
      expect(screen.getByText(row.label)).toBeInTheDocument();
      expect(screen.getByText(row.desc)).toBeInTheDocument();
    });

    // Check likelihood columns
    likelihoodCols.forEach(col => {
      expect(screen.getByText(col.label)).toBeInTheDocument();
      expect(screen.getByText(col.desc)).toBeInTheDocument();
    });
  });

  it('renders all risk code tiles', () => {
    render(<InitialRiskRatingModal show={true} onHide={onHideMock} />);

    consequenceRows.forEach(row => {
      row.codes.forEach(code => {
        expect(screen.getByText(code)).toBeInTheDocument();
      });
    });
  });

  it('updates selected state when selectedValue prop changes', async () => {
    const {rerender} = render(
      <InitialRiskRatingModal
        show={true}
        onHide={onHideMock}
        selectedValue="A1"
      />,
    );

    let selectedTile = screen.getByText('A1');
    expect(selectedTile).toHaveStyle('border: 2px solid');

    // Change selectedValue prop
    rerender(
      <InitialRiskRatingModal
        show={true}
        onHide={onHideMock}
        selectedValue="B2"
      />,
    );

    await waitFor(() => {
      const newSelectedTile = screen.getByText('B2');
      expect(newSelectedTile).toHaveStyle('border: 2px solid');
    });

    // Previous tile should not be selected
    const previousTile = screen.getByText('A1');
    expect(previousTile).not.toHaveStyle('border: 2px solid');
  });

  it('handles empty selectedValue prop', () => {
    render(
      <InitialRiskRatingModal
        show={true}
        onHide={onHideMock}
        selectedValue=""
      />,
    );

    // No tile should be selected
    consequenceRows.forEach(row => {
      row.codes.forEach(code => {
        const tile = screen.getByText(code);
        expect(tile).not.toHaveStyle('border: 2px solid');
      });
    });
  });

  describe('Disabled tiles functionality', () => {
    it('disables tiles with higher risk than irrValue', () => {
      render(
        <InitialRiskRatingModal
          show={true}
          onHide={onHideMock}
          onSelect={onSelectMock}
          irrValue="C3"
        />,
      );

      // C4, C5, D4, D5, E3, E4, E5 should be disabled (higher than C3)
      const disabledCodes = ['C4', 'C5', 'D4', 'D5', 'E3', 'E4', 'E5'];
      disabledCodes.forEach(code => {
        const tile = screen.getByText(code);
        expect(tile).toBeDisabled();
        expect(tile).toHaveStyle('cursor: not-allowed');
        expect(tile).toHaveStyle('pointerEvents: none');
      });

      // Lower risk codes should not be disabled
      const enabledCodes = ['A1', 'B2', 'C1', 'C2', 'C3'];
      enabledCodes.forEach(code => {
        const tile = screen.getByText(code);
        expect(tile).not.toBeDisabled();
        expect(tile).toHaveStyle('cursor: pointer');
        expect(tile).toHaveStyle('pointerEvents: auto');
      });
    });

    it('tests compareRiskCodes function through different irrValue scenarios', () => {
      // Test with A1 as irrValue - only A1 should be enabled
      const {rerender} = render(
        <InitialRiskRatingModal
          show={true}
          onHide={onHideMock}
          irrValue="A1"
        />,
      );

      // A1 should be enabled, all others disabled
      const a1Tile = screen.getByText('A1');
      expect(a1Tile).not.toBeDisabled();

      const higherTiles = ['A2', 'A3', 'B1', 'C1', 'D1', 'E1'];
      higherTiles.forEach(code => {
        const tile = screen.getByText(code);
        expect(tile).toBeDisabled();
      });

      // Test with E5 as irrValue - all should be enabled
      rerender(
        <InitialRiskRatingModal
          show={true}
          onHide={onHideMock}
          irrValue="E5"
        />,
      );

      // All tiles should be enabled when irrValue is E5
      consequenceRows.forEach(row => {
        row.codes.forEach(code => {
          const tile = screen.getByText(code);
          expect(tile).not.toBeDisabled();
        });
      });
    });

    it('tests compareRiskCodes with same row different columns', () => {
      render(
        <InitialRiskRatingModal
          show={true}
          onHide={onHideMock}
          irrValue="C3"
        />,
      );

      // C1, C2, C3 should be enabled (same row, lower or equal column)
      const enabledSameRow = ['C1', 'C2', 'C3'];
      enabledSameRow.forEach(code => {
        const tile = screen.getByText(code);
        expect(tile).not.toBeDisabled();
      });

      // C4, C5 should be disabled (same row, higher column)
      const disabledSameRow = ['C4', 'C5'];
      disabledSameRow.forEach(code => {
        const tile = screen.getByText(code);
        expect(tile).toBeDisabled();
      });
    });

    it('tests compareRiskCodes with different rows same columns', () => {
      render(
        <InitialRiskRatingModal
          show={true}
          onHide={onHideMock}
          irrValue="C2"
        />,
      );

      // A2, B2, C2 should be enabled (same column, lower or equal row)
      const enabledSameCol = ['A2', 'B2', 'C2'];
      enabledSameCol.forEach(code => {
        const tile = screen.getByText(code);
        expect(tile).not.toBeDisabled();
      });

      // D2, E2 should be disabled (same column, higher row)
      const disabledSameCol = ['D2', 'E2'];
      disabledSameCol.forEach(code => {
        const tile = screen.getByText(code);
        expect(tile).toBeDisabled();
      });
    });

    it('handles edge case with no irrValue', () => {
      render(
        <InitialRiskRatingModal
          show={true}
          onHide={onHideMock}
          // No irrValue prop
        />,
      );

      // All tiles should be enabled when no irrValue is provided
      consequenceRows.forEach(row => {
        row.codes.forEach(code => {
          const tile = screen.getByText(code);
          expect(tile).not.toBeDisabled();
        });
      });
    });

    it('handles empty irrValue', () => {
      render(
        <InitialRiskRatingModal show={true} onHide={onHideMock} irrValue="" />,
      );

      // All tiles should be enabled when irrValue is empty
      consequenceRows.forEach(row => {
        row.codes.forEach(code => {
          const tile = screen.getByText(code);
          expect(tile).not.toBeDisabled();
        });
      });
    });

    it('shows correct tooltip for disabled tiles', () => {
      render(
        <InitialRiskRatingModal
          show={true}
          onHide={onHideMock}
          irrValue="B2"
        />,
      );

      const disabledTile = screen.getByText('C3');
      const tileContainer = disabledTile.closest('div');
      expect(tileContainer).toHaveAttribute(
        'title',
        'Cannot select a higher risk than Initial Risk Rating',
      );
    });

    it('does not call onSelect when disabled tile is clicked', () => {
      render(
        <InitialRiskRatingModal
          show={true}
          onHide={onHideMock}
          onSelect={onSelectMock}
          irrValue="B2"
        />,
      );

      const disabledTile = screen.getByText('C3');
      fireEvent.click(disabledTile);
      expect(onSelectMock).not.toHaveBeenCalled();
    });

    it('shows tooltip for significant risk reduction', () => {
      render(
        <InitialRiskRatingModal
          show={true}
          onHide={onHideMock}
          irrValue="E5"
          selectedValue="B2"
        />,
      );

      // B2 is significantly lower than E5 (column difference > 2)
      const tile = screen.getByText('B2');
      const tileContainer = tile.closest('div');
      expect(tileContainer).toHaveAttribute(
        'title',
        'You are lowering the Risk significantly. You will have to mention the reason to do so.',
      );
    });
  });

  describe('Utility functions', () => {
    describe('getCellColor', () => {
      it('returns green color for green codes', () => {
        const greenCodes = ['A1', 'A2', 'A3', 'B1', 'B2', 'C1'];
        greenCodes.forEach(code => {
          expect(getCellColor(code)).toBe('#28A747');
        });
      });

      it('returns red color for red codes', () => {
        const redCodes = ['C5', 'D4', 'D5', 'E3', 'E4', 'E5'];
        redCodes.forEach(code => {
          expect(getCellColor(code)).toBe('#D41B56');
        });
      });

      it('returns yellow color for other codes', () => {
        const yellowCodes = [
          'A4',
          'A5',
          'B3',
          'B4',
          'B5',
          'C2',
          'C3',
          'C4',
          'D1',
          'D2',
          'D3',
          'E1',
          'E2',
        ];
        yellowCodes.forEach(code => {
          expect(getCellColor(code)).toBe('#FFC107');
        });
      });
    });

    describe('Edge cases and error handling', () => {
      it('handles invalid risk codes gracefully', () => {
        render(
          <InitialRiskRatingModal
            show={true}
            onHide={onHideMock}
            irrValue="INVALID"
            selectedValue="ALSO_INVALID"
          />,
        );

        // Should still render without crashing
        expect(screen.getByRole('dialog')).toBeInTheDocument();

        // All tiles should be enabled when irrValue is invalid
        consequenceRows.forEach(row => {
          row.codes.forEach(code => {
            const tile = screen.getByText(code);
            expect(tile).not.toBeDisabled();
          });
        });
      });

      it('handles malformed risk codes in comparison', () => {
        render(
          <InitialRiskRatingModal
            show={true}
            onHide={onHideMock}
            irrValue="Z9" // Invalid format
          />,
        );

        // Should still render and all tiles should be enabled
        consequenceRows.forEach(row => {
          row.codes.forEach(code => {
            const tile = screen.getByText(code);
            expect(tile).not.toBeDisabled();
          });
        });
      });
    });
  });

  describe('Component behavior', () => {
    it('calls onSelect without onSelect prop', () => {
      render(
        <InitialRiskRatingModal
          show={true}
          onHide={onHideMock}
          // No onSelect prop
        />,
      );

      const tile = screen.getByText('A1');
      // Should not throw error when clicking without onSelect
      expect(() => fireEvent.click(tile)).not.toThrow();
    });

    it('renders correct tile colors based on getCellColor', () => {
      render(<InitialRiskRatingModal show={true} onHide={onHideMock} />);

      // Test green tile
      const greenTile = screen.getByText('A1');
      expect(greenTile).toHaveStyle('background: #28A747');

      // Test red tile
      const redTile = screen.getByText('E5');
      expect(redTile).toHaveStyle('background: #D41B56');

      // Test yellow tile
      const yellowTile = screen.getByText('A4');
      expect(yellowTile).toHaveStyle('background: #FFC107');
    });

    it('handles modal close via backdrop or escape', () => {
      render(<InitialRiskRatingModal show={true} onHide={onHideMock} />);

      // Find the modal backdrop and simulate click
      const modal = screen.getByRole('dialog');
      expect(modal).toBeInTheDocument();

      // The modal should be visible
      expect(modal).toHaveClass('modal');
    });

    it('updates internal state when tile is clicked', () => {
      render(
        <InitialRiskRatingModal
          show={true}
          onHide={onHideMock}
          onSelect={onSelectMock}
        />,
      );

      const firstTile = screen.getByText('A1');
      fireEvent.click(firstTile);

      // First tile should be selected
      expect(firstTile).toHaveStyle('border: 2px solid');

      // Click another tile
      const secondTile = screen.getByText('B2');
      fireEvent.click(secondTile);

      // Second tile should be selected, first should not
      expect(secondTile).toHaveStyle('border: 2px solid');
      expect(firstTile).not.toHaveStyle('border: 2px solid');
    });
  });
});
