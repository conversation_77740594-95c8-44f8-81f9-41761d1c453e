import {render, screen, waitFor} from '@testing-library/react';
import {
  MostlyUsedCard,
  MostlyUsedCardList,
} from '../../../../src/pages/RATemplateListing/components/MostlyUsedCard';

// Mock dependencies
jest.mock('../../../../src/components/TruncateBasicText', () => {
  return function MockTruncateText({
    text,
    maxLength,
  }: {
    text: string;
    maxLength: number;
  }) {
    const isTruncated = text.length > maxLength;
    const displayText = isTruncated ? text.slice(0, maxLength) + '...' : text;
    return (
      <span data-testid="truncate-text" title={isTruncated ? text : undefined}>
        {displayText}
      </span>
    );
  };
});

jest.mock('../../../../src/components/SingleBadgePopover', () => {
  return function MockSingleBadgePopover({
    items,
    label,
  }: {
    items: string[];
    label: string;
  }) {
    return (
      <div data-testid="single-badge-popover">
        <span data-testid="popover-label">{label}</span>
        <div data-testid="popover-items" style={{display: 'none'}}>
          {items.join(', ')}
        </div>
      </div>
    );
  };
});

jest.mock('../../../../src/utils/common', () => ({
  parseDate: jest.fn((date: string | Date) => {
    if (!date) return undefined;
    if (date === '2023-01-15') return '15 Jan 2023';
    if (date === '2023-12-25') return '25 Dec 2023';
    if (date === 'invalid-date') return undefined;
    return '01 Jan 2023'; // Default fallback
  }),
}));

jest.mock('../../../../src/utils/user', () => ({
  getInitials: jest.fn((name: string) => {
    if (!name) return '';
    const names = name.split(' ');
    if (names.length === 1) return names[0].substring(0, 2).toUpperCase();
    return names
      .map(n => n[0])
      .join('')
      .substring(0, 2)
      .toUpperCase();
  }),
}));

jest.mock(
  '../../../../src/pages/RATemplateListing/components/ActionDropdownMenu',
  () => ({
    ActionDropdownMenu: function MockActionDropdownMenu({
      menuAlign,
      data,
      userDetails,
    }: {
      menuAlign?: 'horizontal' | 'vertical';
      data?: any;
      userDetails?: any;
    }) {
      return (
        <div
          data-testid="action-dropdown-menu"
          className={
            menuAlign === 'vertical' ? 'menu-vertical' : 'menu-horizontal'
          }
          data-template-id={data?.id}
          data-user-name={userDetails?.full_name}
        >
          <div className="dot"></div>
          <div className="dot"></div>
          <div className="dot"></div>
        </div>
      );
    },
  }),
);

// Mock hooks
jest.mock('../../../../src/hooks/useQuery', () => ({
  useQuery: jest.fn(),
}));

// Mock services
jest.mock('../../../../src/services/services', () => ({
  getMostlyUsedTemplates: jest.fn(),
}));

// Mock react-toastify
jest.mock('react-toastify', () => ({
  toast: {
    error: jest.fn(),
  },
}));

describe('MostlyUsedCard', () => {
  const defaultProps = {
    templateId: 1,
    templateName: 'Test Template',
    riskCategories: [
      {
        id: 1,
        template_id: 1,
        category_id: 1,
        category_is_other: false,
        status: 1,
        category: {id: 1, name: 'Risk 1', type: 1},
        value: null,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15T00:00:00Z',
        updatedAt: '2023-01-15T00:00:00Z',
      },
      {
        id: 2,
        template_id: 1,
        category_id: 2,
        category_is_other: false,
        status: 1,
        category: {id: 2, name: 'Risk 2', type: 1},
        value: null,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15T00:00:00Z',
        updatedAt: '2023-01-15T00:00:00Z',
      },
    ],
    hazardCategories: [
      {
        id: 1,
        template_id: 1,
        hazard_id: 1,
        hazard_category_is_other: false,
        status: 1,
        value: null,
        hazard_detail: {id: 1, name: 'Hazard 1', type: 1},
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15T00:00:00Z',
        updatedAt: '2023-01-15T00:00:00Z',
      },
    ],
    keywords: [
      {
        id: 1,
        template_id: 1,
        name: 'keyword1',
        status: 1,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15T00:00:00Z',
        updatedAt: '2023-01-15T00:00:00Z',
      },
      {
        id: 2,
        template_id: 1,
        name: 'keyword2',
        status: 1,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15T00:00:00Z',
        updatedAt: '2023-01-15T00:00:00Z',
      },
    ],
    createdOn: '2023-01-15',
    userName: 'John Doe',
  };

  const renderComponent = (props = {}) => {
    return render(<MostlyUsedCard {...defaultProps} {...props} />);
  };

  describe('Component Rendering', () => {
    it('renders the component with all required elements', () => {
      renderComponent();

      expect(screen.getByTestId('truncate-text')).toBeInTheDocument();
      expect(screen.getByText('Risk Categories: 2')).toBeInTheDocument();
      expect(screen.getByText('Hazard Categories: 1')).toBeInTheDocument();
      expect(screen.getByTestId('single-badge-popover')).toBeInTheDocument();
      expect(screen.getByText('Created on: 15 Jan 2023')).toBeInTheDocument();
      expect(screen.getByText('JD')).toBeInTheDocument(); // User avatar initials
      expect(screen.getByTestId('popover-label')).toHaveTextContent(
        'Keywords: 2',
      );
    });

    it('has the correct CSS class structure', () => {
      const {container} = renderComponent();

      expect(container.querySelector('.mostly-used-card')).toBeInTheDocument();
      expect(container.querySelector('.card-header')).toBeInTheDocument();
      expect(container.querySelector('.template-name')).toBeInTheDocument();
      expect(container.querySelector('.categories')).toBeInTheDocument();
      expect(container.querySelector('.card-footer')).toBeInTheDocument();
      expect(container.querySelector('.created-on')).toBeInTheDocument();
      expect(container.querySelector('.user-avatar')).toBeInTheDocument();
      expect(container.querySelector('.avatar-circle')).toBeInTheDocument();
      expect(screen.getByTestId('action-dropdown-menu')).toBeInTheDocument();
    });

    it('renders menu dots correctly', () => {
      const {container} = renderComponent();

      const dots = container.querySelectorAll('.dot');
      expect(dots).toHaveLength(3);
    });
  });

  describe('Template Name Display', () => {
    it('displays template name through TruncateText component', () => {
      renderComponent({templateName: 'Short Name'});

      const truncateText = screen.getByTestId('truncate-text');
      expect(truncateText).toHaveTextContent('Short Name');
    });

    it('passes correct maxLength to TruncateText component', () => {
      const longTemplateName =
        'This is a very long template name that should be truncated according to the maxLength prop';
      renderComponent({templateName: longTemplateName});

      const truncateText = screen.getByTestId('truncate-text');
      // Our mock truncates at 76 characters
      expect(truncateText).toHaveTextContent(
        longTemplateName.slice(0, 76) + '...',
      );
      expect(truncateText).toHaveAttribute('title', longTemplateName);
    });

    it('handles empty template name', () => {
      renderComponent({templateName: ''});

      const truncateText = screen.getByTestId('truncate-text');
      expect(truncateText).toHaveTextContent('');
    });
  });

  describe('Categories Display', () => {
    it('displays risk categories count correctly', () => {
      const riskCategories = Array.from({length: 10}, (_, i) => ({
        id: i + 1,
        template_id: 1,
        category_id: i + 1,
        category_is_other: false,
        status: 1,
        category: {id: i + 1, name: `Risk ${i + 1}`, type: 1},
        value: null,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15T00:00:00Z',
        updatedAt: '2023-01-15T00:00:00Z',
      }));
      renderComponent({riskCategories});
      expect(screen.getByText('Risk Categories: 10')).toBeInTheDocument();
    });

    it('displays hazard categories count correctly', () => {
      const hazardCategories = Array.from({length: 7}, (_, i) => ({
        id: i + 1,
        template_id: 1,
        hazard_id: i + 1,
        hazard_category_is_other: false,
        status: 1,
        value: null,
        hazard_detail: {id: i + 1, name: `Hazard ${i + 1}`, type: 1},
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15T00:00:00Z',
        updatedAt: '2023-01-15T00:00:00Z',
      }));
      renderComponent({hazardCategories});
      expect(screen.getByText('Hazard Categories: 7')).toBeInTheDocument();
    });

    it('handles zero categories', () => {
      renderComponent({riskCategories: [], hazardCategories: []});
      expect(screen.getByText('Risk Categories: 0')).toBeInTheDocument();
      expect(screen.getByText('Hazard Categories: 0')).toBeInTheDocument();
    });

    it('handles large category numbers', () => {
      const riskCategories = Array.from({length: 999}, (_, i) => ({
        id: i + 1,
        template_id: 1,
        category_id: i + 1,
        category_is_other: false,
        status: 1,
        category: {id: i + 1, name: `Risk ${i + 1}`, type: 1},
        value: null,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15T00:00:00Z',
        updatedAt: '2023-01-15T00:00:00Z',
      }));
      const hazardCategories = Array.from({length: 888}, (_, i) => ({
        id: i + 1,
        template_id: 1,
        hazard_id: i + 1,
        hazard_category_is_other: false,
        status: 1,
        value: null,
        hazard_detail: {id: i + 1, name: `Hazard ${i + 1}`, type: 1},
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15T00:00:00Z',
        updatedAt: '2023-01-15T00:00:00Z',
      }));
      renderComponent({riskCategories, hazardCategories});
      expect(screen.getByText('Risk Categories: 999')).toBeInTheDocument();
      expect(screen.getByText('Hazard Categories: 888')).toBeInTheDocument();
    });
  });

  describe('Keywords Display', () => {
    it('displays keywords count in label', () => {
      const keywords = [
        {id: 1, name: 'key1'},
        {id: 2, name: 'key2'},
        {id: 3, name: 'key3'},
        {id: 4, name: 'key4'},
      ];
      renderComponent({keywords});

      const popoverLabel = screen.getByTestId('popover-label');
      expect(popoverLabel).toHaveTextContent('Keywords: 4');
    });

    it('passes keywords to SingleBadgePopover component', () => {
      const keywords = [
        {id: 1, name: 'react'},
        {id: 2, name: 'typescript'},
        {id: 3, name: 'testing'},
      ];
      renderComponent({keywords});

      const popoverItems = screen.getByTestId('popover-items');
      expect(popoverItems).toHaveTextContent('react, typescript, testing');
    });

    it('handles empty keywords array', () => {
      renderComponent({keywords: []});

      const popoverLabel = screen.getByTestId('popover-label');
      expect(popoverLabel).toHaveTextContent('Keywords: 0');

      const popoverItems = screen.getByTestId('popover-items');
      expect(popoverItems).toHaveTextContent('');
    });

    it('handles single keyword', () => {
      const keywords = [{id: 1, name: 'single-keyword'}];
      renderComponent({keywords});

      const popoverLabel = screen.getByTestId('popover-label');
      expect(popoverLabel).toHaveTextContent('Keywords: 1');

      const popoverItems = screen.getByTestId('popover-items');
      expect(popoverItems).toHaveTextContent('single-keyword');
    });

    it('handles keywords with special characters', () => {
      const keywords = [
        {id: 1, name: 'key-word'},
        {id: 2, name: 'key_word'},
        {id: 3, name: 'key.word'},
        {id: 4, name: 'key@word'},
      ];
      renderComponent({keywords});

      const popoverItems = screen.getByTestId('popover-items');
      expect(popoverItems).toHaveTextContent(
        'key-word, key_word, key.word, key@word',
      );
    });
  });

  describe('Date Display', () => {
    it('displays formatted creation date', () => {
      renderComponent({createdOn: '2023-01-15'});
      expect(screen.getByText('Created on: 15 Jan 2023')).toBeInTheDocument();
    });

    it('handles different date formats', () => {
      renderComponent({createdOn: '2023-12-25'});
      expect(screen.getByText('Created on: 25 Dec 2023')).toBeInTheDocument();
    });

    it('handles invalid date gracefully', () => {
      renderComponent({createdOn: 'invalid-date'});
      expect(screen.getByText('Created on:')).toBeInTheDocument();
    });

    it('handles empty date string', () => {
      renderComponent({createdOn: ''});
      expect(screen.getByText('Created on:')).toBeInTheDocument();
    });
  });

  describe('User Avatar', () => {
    it('displays initials of username in avatar', () => {
      renderComponent({userName: 'Alice Smith'});
      expect(screen.getByText('AS')).toBeInTheDocument();
    });

    it('handles single character username', () => {
      renderComponent({userName: 'X'});
      expect(screen.getByText('X')).toBeInTheDocument();
    });

    it('handles lowercase username', () => {
      renderComponent({userName: 'john doe'});
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('handles username with special characters', () => {
      renderComponent({userName: '@user123'});
      expect(screen.getByText('@U')).toBeInTheDocument();
    });

    it('handles empty username gracefully', () => {
      renderComponent({userName: ''});
      // Should still render avatar circle even with empty username
      const {container} = renderComponent({userName: ''});
      expect(container.querySelector('.avatar-circle')).toBeInTheDocument();
    });
  });

  describe('Props Variations', () => {
    it('handles all minimum values', () => {
      const minProps = {
        templateId: 1,
        templateName: 'A',
        riskCategories: [],
        hazardCategories: [],
        keywords: [],
        createdOn: '2023-01-15',
        userName: 'U',
      };
      renderComponent(minProps);

      expect(screen.getByTestId('truncate-text')).toHaveTextContent('A');
      expect(screen.getByText('Risk Categories: 0')).toBeInTheDocument();
      expect(screen.getByText('Hazard Categories: 0')).toBeInTheDocument();
      expect(screen.getByTestId('popover-label')).toHaveTextContent(
        'Keywords: 0',
      );
      expect(screen.getByText('Created on: 15 Jan 2023')).toBeInTheDocument();
      expect(screen.getByText('U')).toBeInTheDocument();
    });

    it('handles all maximum realistic values', () => {
      const maxProps = {
        templateId: 999,
        templateName:
          'This is a very long template name that should definitely be truncated by the TruncateText component because it exceeds the maximum length limit',
        riskCategories: Array.from({length: 999}, (_, i) => ({
          id: i + 1,
          name: `Risk ${i + 1}`,
        })),
        hazardCategories: Array.from({length: 888}, (_, i) => ({
          id: i + 1,
          name: `Hazard ${i + 1}`,
        })),
        keywords: Array.from({length: 20}, (_, i) => ({
          id: i + 1,
          name: `keyword${i + 1}`,
        })),
        createdOn: '2023-12-25',
        userName: 'Very Long Username That Should Still Work',
      };
      renderComponent(maxProps);

      const truncateText = screen.getByTestId('truncate-text');
      expect(truncateText).toHaveTextContent(
        maxProps.templateName.slice(0, 76) + '...',
      );
      expect(screen.getByText('Risk Categories: 999')).toBeInTheDocument();
      expect(screen.getByText('Hazard Categories: 888')).toBeInTheDocument();
      expect(screen.getByTestId('popover-label')).toHaveTextContent(
        'Keywords: 20',
      );
      expect(screen.getByText('Created on: 25 Dec 2023')).toBeInTheDocument();
      expect(screen.getByText('VL')).toBeInTheDocument();
    });
  });

  describe('Component Structure and Accessibility', () => {
    it('maintains proper DOM structure', () => {
      const {container} = renderComponent();

      const card = container.querySelector('.mostly-used-card');
      expect(card).toBeInTheDocument();

      const header = card?.querySelector('.card-header');
      expect(header).toBeInTheDocument();

      const templateName = header?.querySelector('.template-name');
      expect(templateName).toBeInTheDocument();

      const categories = header?.querySelector('.categories');
      expect(categories).toBeInTheDocument();

      const footer = card?.querySelector('.card-footer');
      expect(footer).toBeInTheDocument();

      const createdOn = footer?.querySelector('.created-on');
      expect(createdOn).toBeInTheDocument();

      const userAvatar = footer?.querySelector('.user-avatar');
      expect(userAvatar).toBeInTheDocument();
    });

    it('has proper avatar structure with menu dots', () => {
      const {container} = renderComponent();

      const avatarCircle = container.querySelector('.avatar-circle');
      expect(avatarCircle).toBeInTheDocument();

      const actionDropdown = screen.getByTestId('action-dropdown-menu');
      expect(actionDropdown).toBeInTheDocument();

      const dots = container.querySelectorAll('.dot');
      expect(dots).toHaveLength(3);
    });
  });

  describe('Integration with Dependencies', () => {
    it('calls parseDate utility with correct parameters', () => {
      const mockParseDate = require('../../../../src/utils/common').parseDate;
      renderComponent({createdOn: '2023-01-15'});

      expect(mockParseDate).toHaveBeenCalledWith('2023-01-15');
    });

    it('passes correct props to TruncateText component', () => {
      const templateName = 'Test Template Name';
      renderComponent({templateName});

      const truncateText = screen.getByTestId('truncate-text');
      expect(truncateText).toHaveTextContent(templateName);
    });

    it('passes correct props to SingleBadgePopover component', () => {
      const keywords = [
        {
          id: 1,
          template_id: 1,
          name: 'test1',
          status: 1,
          created_by: 'user1',
          updated_by: null,
          deleted_at: null,
          deleted_by: null,
          createdAt: '2023-01-15T00:00:00Z',
          updatedAt: '2023-01-15T00:00:00Z',
        },
        {
          id: 2,
          template_id: 1,
          name: 'test2',
          status: 1,
          created_by: 'user1',
          updated_by: null,
          deleted_at: null,
          deleted_by: null,
          createdAt: '2023-01-15T00:00:00Z',
          updatedAt: '2023-01-15T00:00:00Z',
        },
      ];
      renderComponent({keywords});

      const popoverLabel = screen.getByTestId('popover-label');
      expect(popoverLabel).toHaveTextContent('Keywords: 2');

      const popoverItems = screen.getByTestId('popover-items');
      expect(popoverItems).toHaveTextContent('test1, test2');
    });
  });

  describe('Optional Props', () => {
    it('applies custom className when provided', () => {
      const customClass = 'custom-card-class';
      const {container} = renderComponent({className: customClass});

      const card = container.querySelector('.mostly-used-card');
      expect(card).toHaveClass(customClass);
    });

    it('passes menuAlign prop to ActionDropdownMenu', () => {
      renderComponent({menuAlign: 'vertical'});

      const actionDropdown = screen.getByTestId('action-dropdown-menu');
      expect(actionDropdown).toHaveClass('menu-vertical');
    });

    it('passes menuAlign horizontal to ActionDropdownMenu', () => {
      renderComponent({menuAlign: 'horizontal'});

      const actionDropdown = screen.getByTestId('action-dropdown-menu');
      expect(actionDropdown).toHaveClass('menu-horizontal');
    });

    it('hides menu when hideMenu is true', () => {
      renderComponent({hideMenu: true});

      expect(
        screen.queryByTestId('action-dropdown-menu'),
      ).not.toBeInTheDocument();
    });

    it('shows menu when hideMenu is false', () => {
      renderComponent({hideMenu: false});

      expect(screen.getByTestId('action-dropdown-menu')).toBeInTheDocument();
    });

    it('shows menu by default when hideMenu is not provided', () => {
      renderComponent();

      expect(screen.getByTestId('action-dropdown-menu')).toBeInTheDocument();
    });
  });

  describe('ActionDropdownMenu Data Structure', () => {
    it('passes correct data structure to ActionDropdownMenu', () => {
      const templateId = 123;
      const templateName = 'Test Template';
      const createdOn = '2023-01-15';
      const userName = 'Test User';

      renderComponent({
        templateId,
        templateName,
        createdOn,
        userName,
      });

      const actionDropdown = screen.getByTestId('action-dropdown-menu');
      expect(actionDropdown).toHaveAttribute(
        'data-template-id',
        templateId.toString(),
      );
      expect(actionDropdown).toHaveAttribute('data-user-name', userName);
    });

    it('passes userDetails with correct structure to ActionDropdownMenu', () => {
      const userName = 'John Smith';
      renderComponent({userName});

      const actionDropdown = screen.getByTestId('action-dropdown-menu');
      expect(actionDropdown).toHaveAttribute('data-user-name', userName);
    });
  });
});

describe('MostlyUsedCardList', () => {
  const mockUseQuery = require('../../../../src/hooks/useQuery').useQuery;
  const mockGetMostlyUsedTemplates =
    require('../../../../src/services/services').getMostlyUsedTemplates;
  const mockToast = require('react-toastify').toast;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockTemplateData = {
    results: [
      {
        id: 1,
        task_requiring_ra: 'Template 1',
        created_by: 'user1',
        created_at: '2023-01-15',
        template_category: [
          {
            id: 1,
            template_id: 1,
            category_id: 1,
            category_is_other: false,
            status: 1,
            category: {id: 1, name: 'Risk 1', type: 1},
            value: null,
            created_by: 'user1',
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
            createdAt: '2023-01-15T00:00:00Z',
            updatedAt: '2023-01-15T00:00:00Z',
          },
        ],
        template_hazards: [
          {
            id: 1,
            template_id: 1,
            hazard_id: 1,
            hazard_category_is_other: false,
            status: 1,
            value: null,
            hazard_detail: {id: 1, name: 'Hazard 1', type: 1},
            created_by: 'user1',
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
            createdAt: '2023-01-15T00:00:00Z',
            updatedAt: '2023-01-15T00:00:00Z',
          },
        ],
        template_keywords: [
          {
            id: 1,
            template_id: 1,
            name: 'keyword1',
            status: 1,
            created_by: 'user1',
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
            createdAt: '2023-01-15T00:00:00Z',
            updatedAt: '2023-01-15T00:00:00Z',
          },
        ],
      },
      {
        id: 2,
        task_requiring_ra: 'Template 2',
        created_by: 'user2',
        created_at: '2023-01-16',
        template_category: [],
        template_hazards: [],
        template_keywords: [],
      },
    ],
    userDetails: [
      {userId: 'user1', full_name: 'John Doe', email: '<EMAIL>'},
      {userId: 'user2', full_name: 'Jane Smith', email: '<EMAIL>'},
    ],
  };

  describe('Loading State', () => {
    it('displays loading state when data is being fetched', () => {
      mockUseQuery.mockReturnValue({
        data: null,
        isLoading: true,
      });

      render(<MostlyUsedCardList />);

      expect(screen.getByText('Mostly Used')).toBeInTheDocument();
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });

  describe('Empty State', () => {
    it('returns null when no data is available', () => {
      mockUseQuery.mockReturnValue({
        data: null,
        isLoading: false,
      });

      const {container} = render(<MostlyUsedCardList />);
      expect(container.firstChild).toBeNull();
    });

    it('returns null when results array is empty', () => {
      mockUseQuery.mockReturnValue({
        data: {results: [], userDetails: []},
        isLoading: false,
      });

      const {container} = render(<MostlyUsedCardList />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Success State', () => {
    beforeEach(() => {
      mockUseQuery.mockReturnValue({
        data: mockTemplateData,
        isLoading: false,
      });
    });

    it('renders title and cards container', () => {
      render(<MostlyUsedCardList />);

      expect(screen.getByText('Mostly Used')).toBeInTheDocument();

      const cardsContainer = document.querySelector('.ra-mostly-used-cards');
      expect(cardsContainer).toBeInTheDocument();
    });

    it('renders MostlyUsedCard components for each template', () => {
      render(<MostlyUsedCardList />);

      // Should render cards for both templates
      const cards = screen.getAllByTestId('action-dropdown-menu');
      expect(cards).toHaveLength(2);
    });

    it('limits display to maximum 4 templates', () => {
      const manyTemplates = {
        ...mockTemplateData,
        results: Array.from({length: 10}, (_, i) => ({
          ...mockTemplateData.results[0],
          id: i + 1,
          task_requiring_ra: `Template ${i + 1}`,
        })),
      };

      mockUseQuery.mockReturnValue({
        data: manyTemplates,
        isLoading: false,
      });

      render(<MostlyUsedCardList />);

      const cards = screen.getAllByTestId('action-dropdown-menu');
      expect(cards).toHaveLength(4); // Should be limited to 4
    });

    it('passes correct props to MostlyUsedCard components', () => {
      render(<MostlyUsedCardList />);

      // Check if template data is passed correctly
      const firstCard = screen.getAllByTestId('action-dropdown-menu')[0];
      expect(firstCard).toHaveAttribute('data-template-id', '1');
      expect(firstCard).toHaveAttribute('data-user-name', 'John Doe');
    });

    it('handles user details mapping correctly', () => {
      render(<MostlyUsedCardList />);

      const cards = screen.getAllByTestId('action-dropdown-menu');

      // First card should have mapped user name
      expect(cards[0]).toHaveAttribute('data-user-name', 'John Doe');

      // Second card should have mapped user name
      expect(cards[1]).toHaveAttribute('data-user-name', 'Jane Smith');
    });

    it('falls back to created_by when user details not found', () => {
      const dataWithMissingUser = {
        ...mockTemplateData,
        results: [
          {
            ...mockTemplateData.results[0],
            created_by: 'unknown_user',
          },
        ],
        userDetails: [], // Empty user details
      };

      mockUseQuery.mockReturnValue({
        data: dataWithMissingUser,
        isLoading: false,
      });

      render(<MostlyUsedCardList />);

      const card = screen.getByTestId('action-dropdown-menu');
      expect(card).toHaveAttribute('data-user-name', 'unknown_user');
    });

    it('passes menuAlign as vertical to cards', () => {
      render(<MostlyUsedCardList />);

      const cards = screen.getAllByTestId('action-dropdown-menu');
      cards.forEach(card => {
        expect(card).toHaveClass('menu-vertical');
      });
    });
  });

  describe('Error Handling', () => {
    it('calls toast.error when onError is triggered', () => {
      let onErrorCallback: ((err: any) => void) | undefined;

      mockUseQuery.mockImplementation((_key: any, _fn: any, options: any) => {
        onErrorCallback = options.onError;
        return {
          data: null,
          isLoading: false,
        };
      });

      render(<MostlyUsedCardList />);

      // Simulate error
      const mockError = {message: 'Network error'};
      onErrorCallback?.(mockError);

      expect(mockToast.error).toHaveBeenCalledWith('Network error');
    });

    it('calls toast.error with default message when error has no message', () => {
      let onErrorCallback: ((err: any) => void) | undefined;

      mockUseQuery.mockImplementation((_key: any, _fn: any, options: any) => {
        onErrorCallback = options.onError;
        return {
          data: null,
          isLoading: false,
        };
      });

      render(<MostlyUsedCardList />);

      // Simulate error without message
      const mockError = {};
      onErrorCallback?.(mockError);

      expect(mockToast.error).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('Hook Integration', () => {
    it('calls useQuery with correct parameters', () => {
      mockUseQuery.mockReturnValue({
        data: mockTemplateData,
        isLoading: false,
      });

      render(<MostlyUsedCardList />);

      expect(mockUseQuery).toHaveBeenCalledWith(
        ['mostly-used-templates'],
        mockGetMostlyUsedTemplates,
        expect.objectContaining({
          onError: expect.any(Function),
        }),
      );
    });
  });
});
