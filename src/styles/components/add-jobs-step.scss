.ra-add-jobs-step-header-title {
  color: #1f4a70;
  font-size: 20px;
  font-weight: 600;
}

.ra-add-jobs-step-header-btn {
  margin-right: 12px;
}
.ra-add-jobs-step-risk-rating-btn {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 50px;
  font-weight: 500;
  font-size: 12px;
  background: #f6f8fa;
  color: #333333;
  border: 1px solid #e0e0e0;
  outline: none;
  text-align: center;
  transition: background 0.2s, color 0.2s;
}
.ra-add-jobs-step-risk-rating-btn.selected {
  border: none;
  color: #222;
}
.ra-add-jobs-step-reason-input {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 15px;
  height: 44px;
  background: #fff;
}
.ra-add-jobs-step-reason-error {
  color: #d41b56;
  font-size: 13px;
  margin-top: 2px;
}
.ra-add-jobs-step-initial-risk-section {
  display: flex;
  gap: 4px;
  flex-direction: column;
}
.ra-add-jobs-step-initial-risk-title {
  font-weight: 500;
  font-size: 14px;
}
.ra-add-jobs-step-initial-risk-subtitle {
  font-weight: 400;
  font-size: 14px;
  color: #6c757d;
}
.ra-add-jobs-step-initial-risk-list {
  flex-direction: column;
}
.ra-add-jobs-step-initial-risk-item {
  gap: 12px;
  height: 44px;
  justify-content: space-between;
  padding-top: 12px;
  padding-right: 16px;
  padding-bottom: 12px;
  padding-left: 16px;
  border-radius: 6px;
  border: 1px solid var(--border-quarternary, #dee2e6);
}
.ra-add-jobs-step-initial-risk-label {
  min-width: 100px;
  font-weight: 600;
  color: #1f4a70;
  font-size: 14px;
}
.ra-add-jobs-step-residual-risk-section {
  font-weight: 500;
  margin-bottom: 8px;
}
.ra-add-jobs-step-residual-risk-item {
  gap: 12px;
  height: 44px;
  justify-content: space-between;
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid var(--border-quarternary, #dee2e6);
  background: #fff;
}
.ra-add-jobs-step-residual-risk-item.disabled {
  background: #f5f5f5;
  opacity: 0.6;
  pointer-events: none;
}
.ra-add-jobs-step-residual-risk-label {
  min-width: 100px;
  font-weight: 600;
  color: #1f4a70;
  font-size: 14px;
}
.ra-add-jobs-step-job-card-header {
  display: flex !important;
  cursor: pointer !important;
  background: #ffffff !important;
  height: 100% !important;
  flex-direction: row !important;
}
.ra-add-jobs-step-job-card-title {
  color: #1f4a70;
  text-decoration: underline;
  font-size: 14px;
}
.ra-add-jobs-step-job-card-arrow {
  padding: 8px 4px;
  display: flex;
  align-items: center;
  span {
    margin-left: 8px;
  }
}
.ra-add-jobs-step-job-card-step {
  margin-left: 8px;
  margin-top: 4px;
  font-size: 16px;
  font-weight: 600;
}

.ra-hazard-header-button {
  font-weight: 400;
  font-size: 14p;
  color: #1f4a70;
  text-decoration: underline;
  text-decoration-style: solid;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}
