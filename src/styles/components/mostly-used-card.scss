.ra-mostly-used-cards {
  display: flex;
  align-items: center;
  gap: 16px;
  background-color: #ffffff;
  margin-bottom: 12px;
}

.mostly-used-title {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #1f4a70;
  margin-bottom: 12px;
  color: #333333;
}

.mostly-used-card {
  all: unset;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;

  width: 340px;
  height: 182px;

  border: 1px solid #cccccc;
  border-radius: 4px;
  background-color: inherit;

  &-selected {
    border: 2px solid #1f4a70;
    border-radius: 4px;
  }

  &-focus {
    cursor: pointer;

    &:focus,
    &:hover,
    &:active {
      border: 2px solid #1f4a70;
      border-radius: 4px;
    }
  }
}

.card-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  padding: 12px;
  gap: 4px;

  width: 100%;
  height: 134px;
  background-color: inherit;
}

.template-name {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #1f4a70;
  margin-bottom: auto;
}

.categories {
  width: 100%;
  height: 64px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  gap: 4px;

  div {
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    color: #333333;
  }
}

.card-footer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  gap: 21px;

  width: 100%;
  height: 46px;

  border-top: 1px solid #cccccc;
  background-color: inherit;

  .created-on {
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: #6c757d;
  }

  .user-avatar {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;

    .avatar-circle {
      width: 30px;
      height: 30px;
      background: #e5f4f8;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;

      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      color: #1f4a70;
    }

    .menu-vertical {
      width: 20px;
      height: 20px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .dot {
        width: 4px;
        height: 4px;
        background: #1f4a70;
        border-radius: 50%;
      }
    }
  }
}
