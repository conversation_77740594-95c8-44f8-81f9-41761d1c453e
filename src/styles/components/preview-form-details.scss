.preview-form-details {
  .no-edits-text-yellow {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px 8px;
    gap: 10px;

    background: #fff9e8;
    border-radius: 4px;

    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #bf7f05;
    margin-left: 12px;
  }

  .no-edits-text-red {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 12px 16px;
    background: #faf2f5;
    border-radius: 4px;
    height: 44px;
    font-size: 14px;
    line-height: 20px;
    color: #c82333;
    margin-bottom: 8px;

    span {
      font-weight: 600;
    }
  }

  // Card and section styles
  .form-section-card {
    background: #fff;
    border-radius: 6px;
    border: 1px solid #cccccc;
    padding: 16px;
  }

  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f4a70;
  }

  .section-subtitle {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
  }

  .section-divider {
    margin: 0 -20px 16px -20px;
    border-color: #dee2e6;
  }

  // Edit button styles
  .edit-button {
    text-decoration: none;
    font-weight: 400;
    font-size: 14px;
    color: #1f4a70;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  // Risk rating display
  .overall-risk-rating {
    .rating-badge {
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      padding: 2px 8px;
      align-self: end;
      margin-top: 0.5rem;
    }
  }

  // Field display styles
  .field-label {
    font-size: 14px;
    font-weight: 600;
  }

  .field-value {
    font-size: 14px;
    font-weight: 400;
  }

  // User profile button
  .user-profile-button {
    color: #1f4a70;
    text-decoration: underline;
    font-weight: 400;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
  }

  // Badge styles
  .badge-keyword {
    font-size: 14px;
    font-weight: 400;
  }

  .category-badges-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  // At risk section styles
  .at-risk-category-title {
    font-weight: 600;
    color: #1f4a70;
  }

  .at-risk-badges-row {
    .at-risk-badges-container {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
  }

  // Hazard table styles
  .hazard-table-section {
    background: #fff;
    border-radius: 6px;
  }

  .hazard-table-title {
    font-weight: bold;
  }

  .hazard-table-buttons {
    display: flex;
    gap: 12px;
  }

  // Card specific styles
  .team-members-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .team-member-row {
    display: flex;
    gap: 12px;
  }

  .team-member-item {
    flex: 1;
  }

  // Main container styles
  .preview-form-container {
    margin: 0px 0px 68px 0px;
  }

  // Bottom margin utility
  .mb-16 {
    margin-bottom: 16px;
  }

  .mb-12 {
    margin-bottom: 12px;
  }

  .mb-8 {
    margin-bottom: 8px;
  }

  // Padding utilities
  .p-16 {
    padding: 16px;
  }

  // Gap utilities
  .gap-16 {
    gap: 16px;
  }

  .gap-12 {
    gap: 12px;
  }

  // Typography utilities
  .fs-20 {
    font-size: 20px;
  }

  .fs-16 {
    font-size: 16px;
  }

  .fs-14 {
    font-size: 14px;
  }

  .fs-12 {
    font-size: 12px;
  }

  .fw-600 {
    font-weight: 600;
  }

  .fw-500 {
    font-weight: 500;
  }

  .fw-400 {
    font-weight: 400;
  }

  // Color utilities
  .text-primary {
    color: #1f4a70;
  }

  .text-secondary {
    color: #333333;
  }

  .text-muted {
    color: #6c757d;
  }

  // Width utilities
  .w-290p {
    width: 290px;
  }

  // Height utilities
  .h-390p {
    height: 390px;
  }

  .edit-category-button {
    font-size: 14px;
    font-weight: 400;
    color: #1f4a70;
  }
}

// Custom sticky styles for PreviewFormDetails table
.sticky-styles-sr-no {
  position: sticky !important;
  left: 0 !important;
  background: #fff !important;
  z-index: 2 !important;
}

.sticky-styles-job-steps {
  position: sticky !important;
  left: 65px !important; // Width of Sr. No. column
  background: #fff !important;
  z-index: 2 !important;
}

.border-right-col {
  border-right: 1px solid #dee2e6 !important;
}
