.ra-initial-risk-modal {
  max-width: 1350px !important;
  width: 100%;
}

.ra-initial-risk-modal-body {
  padding: 32px;
}

.ra-initial-risk-modal-title {
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 16px;
}

.ra-initial-risk-modal-table-container {
  overflow-x: auto;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
}

.ra-initial-risk-modal-table {
  display: grid;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  min-width: 1200px;
  background: #fff;
}

.ra-initial-risk-modal-cell {
  background: #f6f8fa;
  border-right: 1px solid #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 12px 8px;
  font-size: 12px;
}

.ra-initial-risk-modal-cell-vertical {
  writing-mode: vertical-rl;
  transform: rotate(-180deg);
}

.ra-initial-risk-modal-cell-people {
  border-bottom: 1px solid #e0e0e0 !important;
  align-items: center !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  letter-spacing: 1 !important;
}

.ra-initial-risk-modal-cell-consequence {
  border-bottom: 1px solid #e0e0e0 !important;
  align-items: center !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  letter-spacing: 1 !important;
}

.ra-initial-risk-modal-likelihood-label {
  font-size: 14px !important;
  min-height: 80px !important;
  width: 200px !important;
  padding: 8px !important;
  align-items: center !important;
}

.ra-initial-risk-modal-likelihood-desc {
  font-size: 12px !important;
  min-height: 80px !important;
  width: 200px !important;
  padding: 8px !important;
  align-items: center !important;
}

.ra-initial-risk-modal-tile-btn {
  color: #fff;
  font-weight: 600;
  font-size: 18px;
  width: 200px;
  height: 112px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
  text-align: center;
  text-decoration: none;
  margin: 0;
  padding: 0;
  transition: box-shadow 0.2s, border 0.2s;
  appearance: none;
}
