import React, {useEffect, useState} from 'react';
import {Row, Col, Form} from 'react-bootstrap';
import CheckboxComponent from './CheckboxComponent';
import {format} from 'date-fns';
import LevelOfRATag from './LevelOfRATag';
import {renderWithTooltipIfNeeded} from '../utils/renderWithTooltipIfNeeded';

import '../styles/components/grouped-checkbox-grid.scss';

type GroupedCheckboxGridProps = Readonly<{
  title: string;
  subtitle: string;
  groups: {
    id: number;
    label: string;
    options: {id: number; label: string}[];
    columns?: number;
  }[];
  value: {
    is_other: boolean;
    parameter_type_id: number;
    parameter_id: number[];
    value: string;
  }[];
  onChange: (params: GroupedCheckboxGridProps['value']) => void;
  othersPlaceholder?: string; // not used
  othersMaxLength?: number;
  isEdit?: boolean;
  dateOfRiskAssessment?: string;
  isLeve1RA?: boolean;
}>;

export default function GroupedCheckboxGrid({
  title,
  subtitle,
  groups,
  value,
  onChange,
  othersMaxLength = 2000,
  isEdit = false,
  dateOfRiskAssessment,
  isLeve1RA = false,
}: GroupedCheckboxGridProps) {
  // Build local state from value prop
  const [state, setState] = useState<
    Record<number, {checked: Set<number>; othersText: string; isOther: boolean}>
  >({});

  // Sync local state with value prop
  useEffect(() => {
    const newState: typeof state = {};
    groups.forEach(group => {
      const param = value.find(v => v.parameter_type_id === group.id) || {
        is_other: false,
        parameter_type_id: group.id,
        parameter_id: [],
        value: '',
      };
      newState[group.id] = {
        checked: new Set(param?.parameter_id),
        othersText: param?.value || '',
        isOther: param?.is_other,
      };
    });
    setState(newState);
    // eslint-disable-next-line
  }, [groups, value]);

  // Helper to update parent form
  const updateForm = (nextState: typeof state) => {
    const params = groups.map(group => {
      const groupState = nextState[group.id];
      return {
        is_other: groupState.isOther,
        parameter_type_id: group.id,
        parameter_id: Array.from(groupState.checked),
        value: groupState.isOther ? groupState.othersText : '',
      };
    });
    onChange(params);
  };

  // Handle checkbox change
  const handleCheck = (
    groupId: number,
    optionId: number,
    isOthers: boolean,
  ) => {
    setState(prev => {
      const checked = new Set(prev[groupId]?.checked || []);
      let isOther = prev[groupId]?.isOther || false;
      let othersText = prev[groupId]?.othersText || '';
      if (isOthers) {
        isOther = !isOther;
        if (!isOther) othersText = '';
      } else if (checked.has(optionId)) checked.delete(optionId);
      else checked.add(optionId);

      const nextState = {
        ...prev,
        [groupId]: {checked, othersText, isOther},
      };
      updateForm(nextState);
      return nextState;
    });
  };

  // Handle "Others" text change
  const handleOthersText = (groupId: number, value: string) => {
    setState(prev => {
      const nextState = {
        ...prev,
        [groupId]: {
          ...prev[groupId],
          othersText: value.slice(0, othersMaxLength),
        },
      };
      updateForm(nextState);
      return nextState;
    });
  };

  return (
    <div>
      <div>
        {!isEdit && (
          <div className="ra-checkbox-grid-title">
            {renderWithTooltipIfNeeded(title)}
          </div>
        )}
        <div className="d-flex align-items-center">
          {dateOfRiskAssessment && (
            <div className="text-muted fs-14 d-flex align-items-center">
              Date of Risk Assessment:{' '}
              {format(new Date(dateOfRiskAssessment), 'dd MMM yyyy')}
            </div>
          )}
          {isLeve1RA && <LevelOfRATag />}
        </div>
        {!isEdit && <hr className="ra-checkbox-grid-hr" />}
        <div className="ra-checkbox-grid-subtitle">{subtitle}</div>
        {groups.map(group => {
          const columns = group.columns || 3;
          // Flatten options into columns
          const colOptions: {id: number; label: string}[][] = Array.from(
            {length: columns},
            () => [],
          );
          group.options.forEach((option, i) => {
            colOptions[i % columns].push(option);
          });

          // Find which column should get the "Others" checkbox (the column with the fewest options, or the last if equal)
          let minColIdx = 0;
          let minLen = colOptions[0].length;
          for (let i = 1; i < columns; i++) {
            if (colOptions[i].length < minLen) {
              minColIdx = i;
              minLen = colOptions[i].length;
            }
          }

          return (
            <div key={group.id} className="ra-checkbox-grid-group">
              <div className="ra-checkbox-grid-group-label">{group.label}</div>
              <Row>
                {colOptions.map((options, colIdx) => (
                  <Col key={`${group.id}`}>
                    {options.map(option => (
                      <div key={option.id} className="ra-checkbox-grid-option">
                        <CheckboxComponent
                          id={`${group.id}-${option.id}`}
                          checked={
                            state[group.id]?.checked.has(option.id) || false
                          }
                          label={option.label}
                          onChange={() =>
                            handleCheck(group.id, option.id, false)
                          }
                        />
                      </div>
                    ))}
                    {/* Place "Others" checkbox in the column with the fewest options */}
                    {colIdx === minColIdx && (
                      <div className="ra-checkbox-grid-option">
                        <CheckboxComponent
                          id={`${group.id}-others`}
                          checked={state[group.id]?.isOther || false}
                          label="Others"
                          onChange={() => handleCheck(group.id, -1, true)}
                        />
                      </div>
                    )}
                  </Col>
                ))}
              </Row>
              {/* Render the textbox for "Others" in a new row below all columns */}
              {state[group.id]?.isOther && (
                <Row>
                  <Col md={8}>
                    <Form.Control
                      type="text"
                      placeholder={`List the ${
                        group.label.charAt(0).toUpperCase() +
                        group.label.slice(1).toLowerCase()
                      } at Risk`}
                      value={state[group.id]?.othersText || ''}
                      maxLength={othersMaxLength}
                      onChange={e => handleOthersText(group.id, e.target.value)}
                      className="ra-checkbox-grid-others-input"
                    />
                  </Col>
                </Row>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
