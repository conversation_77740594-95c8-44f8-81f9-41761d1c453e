import React from 'react';
import {CheckFilled, CheckUnFilled} from '../utils/svgIcons';
import classNames from 'classnames';

import '../styles/components/checkbox-component.scss';

const CheckboxComponent = ({
  checked,
  label,
  onChange,
  id,
  className,
}: {
  checked: boolean;
  label?: string | React.ReactNode;
  onChange: () => void;
  id: string;
  className?: string;
}) => (
  // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
  <label
    htmlFor={id}
    className={classNames(
      'ra-checkbox-component ra-form-check-box fs-14',
      className,
    )}
    // eslint-disable-next-line jsx-a11y/no-noninteractive-element-to-interactive-role
    role="checkbox"
    onClick={e => e.stopPropagation()} // Prevent click from bubbling to parent
    onKeyDown={e => {
      if (e.key === ' ' || e.key === 'Enter') {
        e.preventDefault();
        onChange();
      }
    }}
    tabIndex={0}
    aria-checked={checked}
  >
    <span className="ra-checkbox-component-icon">
      {checked ? <CheckFilled /> : <CheckUnFilled />}
    </span>
    <input
      type="checkbox"
      id={id}
      checked={checked}
      onChange={event => {
        event.stopPropagation();
        onChange();
      }}
      onClick={event => {
        event.stopPropagation();
      }}
      className="ra-display-none"
    />
    {label && <span className="ra-form-check-box-label">{label}</span>}
  </label>
);
export default CheckboxComponent;
