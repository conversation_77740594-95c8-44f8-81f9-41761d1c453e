import React, {useReducer} from 'react';
import classNames from 'classnames';
import {createPortal} from 'react-dom';
import {CloseIcon} from './icons';

import '../styles/components/drawer.scss';

interface DrawerProps {
  trigger: React.ReactElement;
  title?: string;
  children?:
    | React.ReactNode
    | ((props: {closeDrawer: () => void}) => React.ReactElement<any>);
  className?: string;
  position?: 'start' | 'end';
  notUsePortal?: boolean;
  onClose?: () => void;
}

const Drawer: React.FC<DrawerProps> = props => {
  const [isOpen, toggle] = useReducer(t => !t, false);
  const {
    title = '',
    trigger,
    children,
    className,
    position = 'end',
    notUsePortal = false,
    onClose,
  } = props;

  const closeDrawer = () => toggle();
  const manuallyCloseDrawer = () => {
    onClose?.();
    closeDrawer();
  };

  const ele = (
    <div
      className={classNames(className, 'ra-drawer', {
        hide: !isOpen,
        [position + '-0']: true,
      })}
      onTransitionEnd={() => {
        if (!isOpen) toggle();
      }}
    >
      <div className="ra-drawer-header">
        {title}
        <button className="unset ra-drawer-close" onClick={manuallyCloseDrawer}>
          <CloseIcon className="cursor-pointer" />
        </button>
      </div>
      <div className="ra-drawer-content">
        {isOpen &&
          (typeof children === 'function' ? children({closeDrawer}) : children)}
      </div>
    </div>
  );

  if (notUsePortal) return ele;

  return (
    <>
      {React.cloneElement(trigger, {onClick: toggle})}
      {isOpen && createPortal(ele, document.body)}
    </>
  );
};

export default Drawer;
