/* eslint-disable @typescript-eslint/ban-ts-comment */
import React, {useMemo} from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  flexRender,
  ColumnDef,
  getExpandedRowModel,
  ExpandedState,
} from '@tanstack/react-table';
import {Table} from 'react-bootstrap';
import {LiaSortSolid, LiaSortUpSolid, LiaSortDownSolid} from 'react-icons/lia';
import classNames from 'classnames';

import '../styles/components/infinite-scroll-table.scss';
import {NoResultsFound} from '../utils/svgIcons';

const renderSortIcon = (isSorted: false | string) => {
  if (isSorted === 'asc') {
    return (
      <div data-testid="sort-icon">
        {/* @ts-ignore-next-line */}
        <LiaSortUpSolid />
      </div>
    );
  } else if (isSorted === 'desc') {
    return (
      <div data-testid="sort-icon">
        {/* @ts-ignore-next-line */}
        <LiaSortDownSolid />
      </div>
    );
  } else {
    return (
      <div data-testid="sort-icon">
        {/* @ts-ignore-next-line */}
        <LiaSortSolid />
      </div>
    );
  }
};

export interface InfiniteScrollTableProps<T extends object> {
  data: T[];
  columns: ColumnDef<T>[];
  isLoading?: boolean;
  isFetchingNextPage?: boolean;
  pagination: any;
  sorting: {
    sorting: Array<{id: string; desc: boolean}>;
    onSortingChange: (params: Array<{id: string; desc: boolean}>) => void;
  };
  tableContainerStyle?: React.CSSProperties;
  tableDataRowClassName?: string;
  rowSelection?: Record<string, boolean>;
  fetchNextPage: () => void;
  subRowKey?: any;
  onRowClick?: (rowData: T) => void;
}

function InfiniteScrollTable<T extends object>({
  data: initialData,
  columns,
  isLoading,
  isFetchingNextPage,
  pagination,
  sorting,
  tableContainerStyle,
  tableDataRowClassName,
  rowSelection,
  fetchNextPage,
  subRowKey,
  onRowClick,
}: Readonly<InfiniteScrollTableProps<T>>) {
  //we need a reference to the scrolling element for logic down below
  const tableContainerRef = React.useRef<HTMLDivElement>(null);
  const [expanded, setExpanded] = React.useState<ExpandedState>({});

  // Use memo to only re-create data when initialData changes
  const data = useMemo(() => initialData, [initialData]);

  // Handle sorting changes
  const handleSortingChange = React.useCallback(
    (updater: any) => {
      // If updater is a function, call it with the current sorting state
      const newSorting =
        typeof updater === 'function' ? updater(sorting.sorting) : updater;
      sorting.onSortingChange(newSorting);
    },
    [sorting.onSortingChange, sorting.sorting],
  );

  // Configure the table instance
  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection,
      expanded,
      sorting: sorting.sorting,
    },
    manualSorting: true,
    enableExpanding: true,
    onExpandedChange: setExpanded,
    getSubRows: (row: T) => {
      if (subRowKey) {
        const subRows = (row as Record<string, unknown>)[subRowKey];
        return Array.isArray(subRows) ? (subRows as T[]) : undefined;
      }
      return undefined;
    },
    onSortingChange: handleSortingChange,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
  });

  //called on scroll and possibly on mount to fetch more data as the user scrolls and reaches bottom of table
  const fetchMoreOnBottomReached = React.useCallback(
    (containerRefElement?: HTMLDivElement | null) => {
      if (containerRefElement) {
        const {scrollHeight, scrollTop, clientHeight} = containerRefElement;
        // Only proceed if we have actual scrollable content (scrollHeight > clientHeight)
        // This prevents the infinite fetch when scrollHeight equals clientHeight
        if (scrollHeight > clientHeight) {
          // Once the user has scrolled within 300px of the bottom of the table, fetch more data if we can
          const scrolledToBottom =
            scrollHeight - scrollTop - clientHeight < 300;

          if (
            scrolledToBottom &&
            !isFetchingNextPage &&
            pagination.page < pagination.totalPages
          ) {
            fetchNextPage();
          }
        } else if (
          // Handle the case when all content fits in view but there are more pages
          !isFetchingNextPage &&
          pagination.page < pagination.totalPages &&
          // Limit to one auto-fetch to prevent loops
          initialData.length < pagination.pageSize
        ) {
          fetchNextPage();
        }
      }
    },
    [
      fetchNextPage,
      isFetchingNextPage,
      pagination.page,
      pagination.totalPages,
      pagination.pageSize,
      initialData.length,
    ],
  );

  //a check on mount and after a fetch to see if the table is already scrolled to the bottom and immediately needs to fetch more data
  React.useEffect(() => {
    // Only check for more data on mount or when data changes
    // Add a delay to ensure the DOM has updated properly
    const timer = setTimeout(() => {
      if (!isFetchingNextPage) {
        fetchMoreOnBottomReached(tableContainerRef.current);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [fetchMoreOnBottomReached, initialData, isFetchingNextPage]);

  const tableContent = useMemo(() => {
    if (isLoading) {
      return (
        <>
          <tbody>
            <tr>
              <td colSpan={columns.length} className="table-loading-td" />
            </tr>
          </tbody>
          <div className="no-result-overlay table-loading">Loading...</div>
        </>
      );
    }

    if (table.getRowModel().rows.length === 0) {
      return (
        <>
          <tbody>
            <tr>
              <td colSpan={columns.length} className="table-loading-td" />
            </tr>
          </tbody>
          <div className="no-result-overlay d-flex flex-column align-items-center fs-14 text-muted table-loading">
            <NoResultsFound /> <br />
            No Results Found!
          </div>
        </>
      );
    }

    return (
      <tbody>
        {table.getRowModel().rows.map((row, idx) => (
          <tr
            key={row.id}
            className={classNames(
              tableDataRowClassName,
              typeof onRowClick === 'function' && 'clickable-row',
            )}
            onClick={() => onRowClick?.(row.original)}
          >
            {row
              .getVisibleCells()
              .filter(cell => !cell.column.columnDef.enableHiding)
              .map(cell => {
                const {isSticky, stickySide = ''} = (cell.column.columnDef
                  .meta ?? {}) as Record<string, unknown>;
                let stickyClass = isSticky ? 'sticky-styles-right' : '';

                if (stickySide === 'left') {
                  stickyClass = 'sticky-styles-left';
                }
                if (
                  typeof stickySide === 'string' &&
                  stickySide.startsWith('custom-')
                ) {
                  stickyClass = stickySide.replace('custom-', '');
                }
                return (
                  <td key={cell.id} className={stickyClass}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                );
              })}
          </tr>
        ))}
      </tbody>
    );
  }, [
    isLoading,
    table.getRowModel().rows.length,
    JSON.stringify(rowSelection),
    onRowClick,
  ]);

  const isTableEmpty = !isLoading && table.getRowModel().rows.length === 0;

  return (
    <div
      ref={tableContainerRef}
      onScroll={e => {
        fetchMoreOnBottomReached(e.currentTarget);
      }}
      className="table-responsive infinite-scroll-table-container position-relative"
      style={{
        maxHeight: 'calc(100vh - 25px)', // should be a fixed height
        ...tableContainerStyle,
      }}
    >
      <Table hover className={`table ${isTableEmpty ? 'dashed-border' : ''}`}>
        {isTableEmpty ? (
          <></>
        ) : (
          <thead className="header" style={{zIndex: 10}}>
            {table.getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers
                  .filter(header => !header.column.columnDef.enableHiding)
                  .map(header => {
                    const {isSticky, stickySide} = (header.column.columnDef
                      ?.meta ?? {}) as Record<string, unknown>;
                    let stickyClass = isSticky ? 'sticky-styles-right' : '';

                    if (stickySide === 'left') {
                      stickyClass = 'sticky-styles-left';
                    }
                    if (
                      typeof stickySide === 'string' &&
                      stickySide.startsWith('custom-')
                    ) {
                      stickyClass = stickySide.replace('custom-', '');
                    }
                    return (
                      <th
                        key={header.id}
                        onClick={header.column.getToggleSortingHandler()}
                        className={classNames('border-top-0 th', stickyClass)}
                        style={{
                          minWidth: (header.column as any).columnDef.minSize,
                        }}
                      >
                        <div
                          className={classNames(
                            'd-flex align-items-center',
                            getHeaderAlignment(
                              (
                                header.column.columnDef.meta as Record<
                                  string,
                                  string
                                >
                              )?.headerAlign,
                            ),
                          )}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                          {header.column.getCanSort() && (
                            <span className="ms-2 ml-2">
                              {renderSortIcon(
                                header.column.getIsSorted() as false | string,
                              )}
                            </span>
                          )}
                        </div>
                      </th>
                    );
                  })}
              </tr>
            ))}
          </thead>
        )}
        {tableContent}
      </Table>
    </div>
  );
}

export default InfiniteScrollTable;

function getHeaderAlignment(header: string): string {
  if (header === 'center') {
    return 'justify-content-center';
  }
  if (header === 'right') {
    return 'justify-content-end';
  }
  return 'justify-content-start';
}
