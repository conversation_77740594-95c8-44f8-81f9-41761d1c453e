import React, {
  useState,
  useMemo,
  useRef,
  useEffect,
  useLayoutEffect,
} from 'react';
import {ChevronDown} from 'react-bootstrap-icons';
import SearchInput from './SearchInput';
import CheckboxComponent from './CheckboxComponent';
import SingleBadgePopover from './SingleBadgePopover';

import '../styles/components/search-user-dropdown.scss';

interface IUser {
  id: string;
  full_name: string;
  designation?: string;
  email: string;
}

export const getInitials = (name?: string): string => {
  if (!name) return '';
  const names = name.split(' ');
  if (names.length === 1) return names[0].substring(0, 2).toUpperCase();
  return names
    .map(n => n[0])
    .join('')
    .substring(0, 2)
    .toUpperCase();
};

interface UserSelectorDropdownProps {
  options: IUser[];
  value: string[];
  onChange: (selectedUsers: string[]) => void;
  onClose: () => void;
}

const UserSelectorDropdown: React.FC<UserSelectorDropdownProps> = ({
  options = [],
  value,
  onChange,
  onClose,
}) => {
  const [search, setSearch] = useState<string>('');

  const filteredUsers = useMemo<IUser[]>(() => {
    if (!search) {
      return options;
    }
    return options.filter(
      user =>
        user.full_name.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase()) ||
        user.designation?.toLowerCase().includes(search.toLowerCase()),
    );
  }, [options, search]);

  const handleUserSelection = (userId: string): void => {
    if (value.includes(userId)) {
      onChange(value.filter(id => id !== userId));
    } else {
      onChange([...value, userId]);
    }
  };

  const handleSelectAll = (): void => {
    if (filteredUsers.length === 0) return;
    const allFilteredUserIds = filteredUsers.map(user => user.id);
    const allCurrentlySelected = filteredUsers.every(user =>
      value.includes(user.id),
    );
    if (allCurrentlySelected) {
      onChange(value.filter(id => !allFilteredUserIds.includes(id)));
    } else {
      onChange(Array.from(new Set([...value, ...allFilteredUserIds])));
    }
  };

  // Close dropdown on outside click
  const dropdownRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  return (
    <div
      ref={dropdownRef}
      className="user-selector-container user-selector-dropdown"
    >
      <div className="search-bar-section">
        <SearchInput
          placeholder="Search"
          value={search}
          onSearch={e => setSearch(e || '')}
        />
      </div>

      <div className="user-list-section">
        {filteredUsers.length > 0 ? (
          filteredUsers.map(user => (
            <div
              key={user.id}
              className="user-list-item ra-z-index-10"
              role="button"
              tabIndex={0}
              onClick={() => handleUserSelection(user.id)}
              onKeyDown={e => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleUserSelection(user.id);
                }
              }}
            >
              <CheckboxComponent
                key={user.id}
                id={`user-check-${user.id}`}
                checked={value.includes(user.id)}
                // Selection is being handled by the parent component
                onChange={() => {
                  handleUserSelection(user.id);
                }}
              />
              <div className="avatar">{getInitials(user.full_name)}</div>
              <div className="user-info">
                <div className="user-name">{user.full_name}</div>
                <div className="user-details">
                  {user.designation ? `${user.designation} â€¢ ` : null}
                  {user.email}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="empty-list-message">No users found.</div>
        )}
      </div>

      <div className="footer-section">
        <button
          type="button"
          className="select-all-link"
          onClick={handleSelectAll}
        >
          {value.length === options.length ? 'Clear all' : 'Select all'}
        </button>
      </div>
    </div>
  );
};

interface SearchUserDropdownProps {
  value: string[];
  options: IUser[];
  placeholder?: string;
  onChange: (selected: string[]) => void;
  maxDisplayNames?: number; // how many selected names to show before counter
}

const SearchUserDropdown: React.FC<SearchUserDropdownProps> = ({
  value,
  options,
  placeholder = 'Select users',
  onChange,
  maxDisplayNames: _maxDisplayNames = 2,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [maxDisplayNames, setMaxDisplayNames] = useState(2);

  const selectedUsers = options.filter(u => value.includes(u.id));
  const inputRef = useRef<HTMLDivElement>(null);
  const namesMeasureRef = useRef<HTMLSpanElement>(null);
  const counterMeasureRef = useRef<HTMLSpanElement>(null);

  useLayoutEffect(() => {
    if (!inputRef.current || !namesMeasureRef.current) return;
    const inputWidth = inputRef.current.offsetWidth;
    let availableWidth = inputWidth - 40; // leave space for chevron, padding, counter
    if (counterMeasureRef.current && selectedUsers.length > 2) {
      availableWidth -= counterMeasureRef.current.offsetWidth + 8;
    }
    let total = 0;
    let fit = 0;
    const children = Array.from(namesMeasureRef.current.children);
    for (const child of children) {
      const w = (child as HTMLElement).offsetWidth;
      if (total + w > availableWidth) break;
      total += w;
      fit++;
    }
    setMaxDisplayNames(Math.max(1, fit));
  }, [selectedUsers, dropdownOpen]);

  useEffect(() => {
    const handleResize = () => {
      setTimeout(() => {
        if (inputRef.current) {
          setMaxDisplayNames(2); // force re-measure
        }
      }, 100);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const displayFirstNames = selectedUsers
    .slice(0, maxDisplayNames)
    .map(u => u.full_name)
    .join(', ');
  const extraCount = selectedUsers.length - maxDisplayNames;
  const extraNames = selectedUsers.slice(maxDisplayNames).map(u => u.full_name);
  const showCounterOnly = selectedUsers.length > maxDisplayNames;

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      setDropdownOpen(open => !open);
      e.preventDefault();
    }
    if (e.key === 'Escape') {
      setDropdownOpen(false);
    }
  };

  return (
    <div className="user-multiselect-root typeahead-wrapper">
      <div
        className={`user-multiselect-input${
          dropdownOpen ? ' open' : ''
        } typeahead-multiselect form-control`}
        tabIndex={0}
        ref={inputRef}
        onClick={() => setDropdownOpen(open => !open)}
        onKeyDown={handleInputKeyDown}
        aria-haspopup="listbox"
        aria-expanded={dropdownOpen}
        aria-controls="user-selector-dropdown-listbox"
        role="combobox"
      >
        <span
          className="user-multiselect-names"
          title={selectedUsers.map(u => u.full_name).join(', ')}
        >
          <span className="user-multiselect-names-list">
            {selectedUsers.length === 0 ? (
              <span className="user-multiselect-placeholder">
                {placeholder}
              </span>
            ) : (
              displayFirstNames
            )}
          </span>
          {showCounterOnly && (
            <span className="user-multiselect-counter" ref={counterMeasureRef}>
              <SingleBadgePopover
                items={extraNames}
                label={`+${extraCount} More`}
              />
            </span>
          )}
        </span>
        <span className="user-multiselect-chevron">
          <ChevronDown size={14} />
        </span>
        <span
          className="user-multiselect-names-container"
          ref={namesMeasureRef}
        >
          {selectedUsers.map(u => (
            <span key={u.id} className="ra-mr-4px">
              {u.full_name}
              {', '}
            </span>
          ))}
        </span>
      </div>
      {dropdownOpen && (
        <div id="user-selector-dropdown-listbox">
          <UserSelectorDropdown
            options={options}
            value={value}
            onChange={onChange}
            onClose={() => setDropdownOpen(false)}
          />
        </div>
      )}
    </div>
  );
};

export default SearchUserDropdown;
