import React from 'react';
import {Form} from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import {CalendarIcon} from './icons';
import {X} from 'react-bootstrap-icons';
import {format} from 'date-fns';

import '../styles/components/custom-date-picker-with-range.scss';

type CustomDatePickerWithRangeProps = {
  label?: string;
  startDate?: Date;
  endDate?: Date;
  onChange: (dates: [string | undefined, string | undefined]) => void;
  placeholder?: string;
  controlId: string;
  minDate?: Date;
  maxDate?: Date;
};

const CustomDatePickerWithRange: React.FC<CustomDatePickerWithRangeProps> = ({
  label,
  startDate,
  endDate,
  onChange,
  placeholder = 'Select Date Range',
  controlId,
  minDate,
  maxDate,
}) => {
  const handleChange = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    const formattedStart = start ? format(start, 'yyyy-MM-dd') : undefined;
    const formattedEnd = end ? format(end, 'yyyy-MM-dd') : undefined;
    onChange([formattedStart, formattedEnd]);
  };

  return (
    <Form.Group controlId={controlId}>
      {label && <Form.Label className="fs-14 fw-500">{label}</Form.Label>}
      <div className="position-relative ra-datepicker">
        <DatePicker
          className="form-control datepicker-input fs-14 pr-4 "
          wrapperClassName="w-100"
          selected={startDate}
          aria-label={label}
          onChange={handleChange}
          startDate={startDate}
          endDate={endDate}
          selectsRange
          placeholderText={placeholder}
          dateFormat="dd MMM yyyy"
          isClearable
          minDate={minDate}
          maxDate={maxDate}
          onKeyDown={e => e.preventDefault()}
        />
        {(startDate || endDate) && (
          <div
            className="position-absolute clear-icon"
            data-testid="clear-icon"
          >
            <button
              className="unset cursor-pointer"
              onClick={() => onChange([undefined, undefined])}
              aria-label="Clear date range"
            >
              <X size={18} />
            </button>
          </div>
        )}
        {!startDate && !endDate && (
          <div
            className="position-absolute calendar-icon"
            data-testid="calendar-icon"
            aria-label="Select date range"
          >
            <CalendarIcon />
          </div>
        )}
      </div>
    </Form.Group>
  );
};

export default CustomDatePickerWithRange;
