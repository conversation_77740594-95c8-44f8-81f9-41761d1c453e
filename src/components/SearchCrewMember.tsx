import React, {useState, useMemo, useRef, useEffect} from 'react';
import {toast} from 'react-toastify';
import SearchInput from './SearchInput';
import {getInitials} from './SearchUserDropdown';
import {useQuery} from '../hooks/useQuery';

import '../styles/components/search-user-dropdown.scss';

interface IUser {
  id: string;
  full_name: string;
  subText: string;
}

interface UserSelectorDropdownProps {
  options: IUser[];
  onChange: (selectedUserId: string[]) => void;
  onClose: () => void;
  isAsync?: boolean;
  search?: string;
}

const UserSelectorDropdown: React.FC<UserSelectorDropdownProps> = ({
  options = [],
  onChange,
  onClose,
  isAsync = false,
  search = '',
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [dropdownStyle, setDropdownStyle] = useState<React.CSSProperties>({});

  useEffect(() => {
    // Calculate position based on input element
    const inputElement = document.activeElement?.closest(
      '.user-multiselect-root',
    );
    if (inputElement && dropdownRef.current) {
      const rect = inputElement.getBoundingClientRect();
      const dropdownHeight = dropdownRef.current.offsetHeight || 350;

      // Check if there's room below the input
      const spaceBelow = window.innerHeight - rect.bottom;
      if (spaceBelow >= dropdownHeight || spaceBelow > rect.top) {
        // Position below
        setDropdownStyle({
          top: rect.bottom + 4,
          left: rect.left,
          maxHeight: `${spaceBelow - 20}px`,
        });
      } else {
        // Position above
        setDropdownStyle({
          bottom: window.innerHeight - rect.top + 4,
          left: rect.left,
          maxHeight: `${rect.top - 20}px`,
        });
      }
    }
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  const handleUserClick = (userId: string) => {
    onChange([userId]);
  };

  const emptyDataText =
    isAsync && search.length < 3
      ? 'Please enter at least 3 characters to search.'
      : 'No users found.';

  return (
    <div
      ref={dropdownRef}
      className="user-selector-container user-selector-dropdown w-100"
      style={dropdownStyle}
    >
      <div className="user-list-section overflowx-hidden">
        {options.length > 0 ? (
          options.map(user => (
            <button
              key={user.id}
              className="user-list-item reset-button ra-z-index-10"
              onClick={() => handleUserClick(user.id)}
            >
              <div className="avatar">{getInitials(user.full_name)}</div>
              <div className="user-info">
                <div className="user-name">{user.full_name}</div>
                <div className="user-details">{user.subText}</div>
              </div>
            </button>
          ))
        ) : (
          <div className="empty-list-message">{emptyDataText}</div>
        )}
      </div>
    </div>
  );
};

interface SearchUserDropdownProps {
  value: string[];
  options: IUser[];
  placeholder?: string;
  onChange: (selected: string[]) => void;
}

const SearchCrewMember: React.FC<SearchUserDropdownProps> = ({
  value,
  options,
  placeholder = 'Select user',
  onChange,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [search, setSearch] = useState('');
  const inputRef = useRef<HTMLDivElement>(null);

  const filteredUsers = useMemo(() => {
    if (!search) return options;
    return options.filter(
      user =>
        user.full_name.toLowerCase().includes(search.toLowerCase()) ||
        user.subText.toLowerCase().includes(search.toLowerCase()),
    );
  }, [options, search]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="user-multiselect-root typeahead-wrapper" ref={inputRef}>
      <div
        className="user-search-input-wrapper"
        onFocus={() => setDropdownOpen(true)}
      >
        <SearchInput
          placeholder={placeholder}
          value={search}
          onSearch={val => {
            setSearch(val || '');
            setDropdownOpen(true);
          }}
          showClear={!!search.length}
        />
      </div>

      {dropdownOpen && (
        <UserSelectorDropdown
          options={filteredUsers}
          onChange={selected => {
            onChange(selected);
            setDropdownOpen(false);
            setSearch('');
          }}
          onClose={() => setDropdownOpen(false)}
        />
      )}
    </div>
  );
};

export default SearchCrewMember;

export interface AsyncSearchCrewMemberProps<T extends object>
  extends Pick<SearchUserDropdownProps, 'value' | 'placeholder'> {
  fetchQuery: (
    ...args: (string | undefined)[]
  ) => Promise<{options: IUser[]; originalData: T[]}>;
  uniqueQueryKey: string;
  onChange: (selected: string[], originalData?: T[]) => void;
}
export const AsyncSearchCrewMember = <T extends object>(
  props: AsyncSearchCrewMemberProps<T>,
) => {
  const {
    value,
    placeholder = 'Select user',
    onChange,
    fetchQuery,
    uniqueQueryKey,
  } = props;
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [search, setSearch] = useState('');
  const inputRef = useRef<HTMLDivElement>(null);

  const {data = {options: [], originalData: []}, isLoading} = useQuery(
    [uniqueQueryKey, search],
    () => fetchQuery(search),
    {
      onError: error =>
        toast.error(`Error fetching office approvers: ${error}`),
    },
  );

  const {originalData, options} = data;

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      const shouldClose = !inputRef.current?.contains(target);

      if (shouldClose) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="user-multiselect-root typeahead-wrapper" ref={inputRef}>
      <div
        className="user-search-input-wrapper"
        onFocus={() => setDropdownOpen(true)}
      >
        <SearchInput
          placeholder={placeholder}
          value={value.join(', ') || search}
          onSearch={val => {
            setSearch(val || '');
            setDropdownOpen(true);
          }}
          disabled={isLoading}
          showClear
          onClear={() => {
            onChange([], originalData);
            setDropdownOpen(false);
          }}
        />
      </div>

      {dropdownOpen && (
        <UserSelectorDropdown
          isAsync
          search={search}
          options={options}
          onChange={selected => {
            onChange(selected, originalData);
            setDropdownOpen(false);
            setSearch('');
          }}
          onClose={() => setDropdownOpen(false)}
        />
      )}
    </div>
  );
};
