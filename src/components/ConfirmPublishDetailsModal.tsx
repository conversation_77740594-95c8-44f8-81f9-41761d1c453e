import React, {useState, useEffect, KeyboardEvent} from 'react';
import {Modal, Button, Col, Form, Row, Badge} from 'react-bootstrap';
import {X} from 'react-bootstrap-icons';
import {useDataStoreContext} from '../context';
import {GACategory} from '../enums/ga-category.enum';

import '../styles/components/confirm-publish-details-modal.scss';

type Props = {
  onClose: () => void;
  keywords: string[];
  onSave: (data: string[]) => void;
};

export const ConfirmPublishDetailsModal: React.FC<Props> = ({
  onClose,
  keywords: initialKeywords,
  onSave,
}) => {
  const {ga4EventTrigger} = useDataStoreContext();
  const [localKeywords, setLocalKeywords] = useState(initialKeywords);
  const [inputValue, setInputValue] = useState('');

  useEffect(() => {
    setLocalKeywords(initialKeywords);
  }, [initialKeywords]);

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      e.preventDefault();
      if (
        inputValue.trim() &&
        !localKeywords.some(
          keyword =>
            keyword.trim().toLowerCase() === inputValue.trim().toLowerCase(),
        )
      ) {
        setLocalKeywords([...localKeywords, inputValue.trim()]);
        setInputValue('');
        ga4EventTrigger(
          'Value',
          'Select_Keywords',
          GACategory.TEMPLATE_PREVIEW,
        );
      }
    }
  };

  const removeKeyword = (keywordToRemove: string) => {
    setLocalKeywords(
      localKeywords.filter(keyword => keyword !== keywordToRemove),
    );
  };

  const handleConfirm = () => {
    onSave(localKeywords);
    onClose();
  };

  return (
    <Modal
      show
      onHide={onClose}
      size="lg"
      backdrop="static"
      dialogClassName="top-modal"
    >
      <Modal.Header>
        <Modal.Title className="fs-20">Confirm Details to Publish</Modal.Title>
      </Modal.Header>
      <Modal.Body className="complete-project-modal">
        <Row className="mb-3">
          <Col>
            <Form.Group controlId="keywordsInput">
              <Form.Label className="fs-14">Select Keywords</Form.Label>
              <Form.Control
                type="text"
                value={inputValue}
                maxLength={255}
                onChange={e => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Type the Keyword and Press Enter to Add"
                className="fs-14"
              />
            </Form.Group>
          </Col>
        </Row>

        <Row className="mt-3">
          <Col>
            <div className="confirm-publish-keywords">
              {localKeywords.map(keyword => (
                <Badge
                  key={keyword}
                  className="d-flex align-items-center py-2 px-2 badge-keyword confirm-publish-badge"
                >
                  {keyword}
                  <X
                    className="confirm-publish-remove"
                    onClick={() => removeKeyword(keyword)}
                    size={20}
                    data-testid={'remove-keyword'}
                  />
                </Badge>
              ))}
            </div>
          </Col>
        </Row>
      </Modal.Body>

      <Modal.Footer>
        <Button variant="primary" className="me-2 fs-14" onClick={onClose}>
          Cancel
        </Button>
        <Button
          variant="secondary"
          className="me-2 fs-14"
          onClick={handleConfirm}
          disabled={localKeywords.length === 0}
        >
          Confirm
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
