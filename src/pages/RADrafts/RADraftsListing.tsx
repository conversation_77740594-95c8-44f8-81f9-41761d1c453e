import React, {useState, useMemo, useCallback} from 'react';
import {ColumnDef} from '@tanstack/react-table';
import InfiniteScrollTable from '../../components/InfiniteScrollTable';
import {useInfiniteQuery} from '../../hooks';
import {getRAList, getTemplateList} from '../../services/services';
import {TemplateListResponse} from '../../types/template';
import {cleanObject, parseDate} from '../../utils/common';
import {Dropdown} from 'react-bootstrap';
import {ThreeDotsMenuIcon} from '../../components/icons';
import TruncateText from '../../components/TruncateBasicText';
import {TemplateStatus} from '../../enums';
import {RADraftHeader} from './RADraftHeader';
import {useNavigate} from 'react-router-dom';
import {DiscardDraftModal} from '../../components/DiscardDraftModal';
import {useDataStoreContext} from '../../context';
import classNames from 'classnames';

export default function RADraftsListing() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<number>(1);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [draftId, setDraftId] = useState<number | null>(null);

  const filter = {
    status: TemplateStatus.DRAFT,
    sort_by: 'updated_at',
    sort_order: 'DESC',
  };

  // Wrapper function to select correct fetcher
  const fetchFunction = useCallback(
    (
      params: {
        page?: number;
        limit?: number;
      } & Record<string, string | number | string[]>,
    ) => (activeTab === 2 ? getTemplateList(params) : getRAList(params)),
    [activeTab],
  );

  const {data, isFetchingNextPage, isLoading, fetchNextPage, refetch} =
    useInfiniteQuery<
      TemplateListResponse['result']['data'][0],
      TemplateListResponse['result']
      // @ts-ignore
    >(fetchFunction, {
      limit: 100,
      ...cleanObject(filter),
    });

  const handleDeleteModalClose = (isRefetch = false) => {
    setShowDeleteModal(false);
    setDraftId(null);
    if (isRefetch) refetch();
  };

  const handleDiscardDraft = (id: number) => {
    setDraftId(id);
    setShowDeleteModal(true);
  };

  const columns = useMemo(
    () =>
      getColumns(
        activeTab,
        data?.pagination?.totalItems,
        navigate,
        handleDiscardDraft,
      ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      JSON.stringify(data.userDetails),
      activeTab,
      data?.pagination?.totalItems,
      navigate,
      handleDiscardDraft,
    ],
  );

  return (
    <div className="ra-body-padding">
      <RADraftHeader activeTab={activeTab} setActiveTab={setActiveTab} />

      <InfiniteScrollTable
        columns={columns}
        data={data.data}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        fetchNextPage={fetchNextPage}
        pagination={data.pagination}
        sorting={{
          sorting: [],
          // eslint-disable-next-line @typescript-eslint/no-empty-function
          onSortingChange: () => {},
        }}
        tableContainerStyle={{minHeight: 300}}
      />

      {showDeleteModal && !!draftId && (
        <DiscardDraftModal
          onClose={handleDeleteModalClose}
          id={draftId}
          activeTab={activeTab}
        />
      )}
    </div>
  );
}

const DarftActionComp = ({
  row,
  selectedTab,
  handleDiscardDraft,
  navigate,
}: {
  row: any;
  selectedTab: number;
  handleDiscardDraft: (id: number) => void;
  navigate: (path: string) => void;
}) => {
  const {
    roleConfig: {
      riskAssessment: {
        canEditDraftRA,
        canEditDraftTemplate,
        canDiscardDraftRA,
        canDiscardDraftTemplate,
      },
    },
  } = useDataStoreContext();
  const rowData = row.original;
  const redirectionUrl = selectedTab === 1 ? 'risks' : 'templates';
  const handleEdit = () => {
    navigate(`/risk-assessment/${redirectionUrl}/${rowData.id}`);
  };
  const handleDiscard = () => {
    handleDiscardDraft(rowData.id);
  };
  const canEdit = selectedTab === 1 ? canEditDraftRA : canEditDraftTemplate;
  const canDiscard =
    selectedTab === 1 ? canDiscardDraftRA : canDiscardDraftTemplate;
  const isDisabled = !(canEdit || canDiscard);
  return (
    <Dropdown
      className={classNames(
        'ra-three-dots-dropdown d-flex align-items-center justify-content-center',
        {
          'cursor-not-allowed': isDisabled,
        },
      )}
    >
      <Dropdown.Toggle
        as="div"
        className={classNames('dropdown-toggle-no-caret', {
          'cursor-not-allowed': isDisabled,
        })}
      >
        <ThreeDotsMenuIcon />
      </Dropdown.Toggle>
      {(canDiscard || canEdit) && (
        <Dropdown.Menu
          className="dropdown-menu-right text-style"
          popperConfig={{
            modifiers: [
              {name: 'preventOverflow', options: {boundary: 'viewport'}},
            ],
          }}
        >
          {canEdit && <Dropdown.Item onClick={handleEdit}>Edit</Dropdown.Item>}
          {canDiscard && (
            <Dropdown.Item onClick={handleDiscard}>Discard Draft</Dropdown.Item>
          )}
        </Dropdown.Menu>
      )}
    </Dropdown>
  );
};
export function getColumns(
  selectedTab: number,
  totalDrafts: number,
  navigate: (path: string) => void,
  handleDiscardDraft: (id: number) => void,
) {
  const columns: ColumnDef<TemplateListResponse['result']['data'][0]>[] = [
    {
      accessorKey: 'task_requiring_ra',
      header: `Drafts (${totalDrafts})`,
      cell: info => (
        <TruncateText text={String(info.getValue())} maxLength={60} />
      ),
      meta: {isSticky: true, stickySide: 'left'},
      enableSorting: false,
      minSize: 220,
    },
    {
      accessorKey: 'updated_at',
      header: 'Last Updated on',
      cell: info => parseDate(info.getValue() as string),
      enableSorting: false,
      minSize: 950,
    },
    {
      id: 'action',
      header: 'Action',
      cell: ({row}) => (
        <DarftActionComp
          row={row}
          selectedTab={selectedTab}
          handleDiscardDraft={handleDiscardDraft}
          navigate={navigate}
        />
      ),
      meta: {
        isSticky: true,
        stickySide: 'right',
        headerAlign: 'center',
      },
      enableSorting: false,
      minSize: 100,
    },
  ];

  return columns;
}
