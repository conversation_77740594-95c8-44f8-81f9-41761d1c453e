import React, {useCallback, useMemo, useState, useEffect} from 'react';
import {Button, Dropdown, Form} from 'react-bootstrap';
import Drawer from '../../../components/Drawer';
import {PlusIcon, TrashIcon} from '../../../components/icons';
import {
  getRaBasicFiltersFormConfig,
  RAFiltersProps,
  RAFilterValues,
  DateRangeValue,
  FilterOption,
  raFiltersInitialState,
} from './RAFilters';
import CustomDatePickerWithRange from '../../../components/CustomDatePickerWithRange';
import CheckboxComponent from '../../../components/CheckboxComponent';

import '../../../styles/components/ra-more-filters-drawer.scss';

interface RAMoreFiltersDrawerProps {
  filters: RAFilterValues;
  onFilterChange: RAFiltersProps['onFilterChange'];
  optionsData: {
    vessels: FilterOption[];
    vesselCategories: FilterOption[];
    offices: FilterOption[];
  };
}

export const RAMoreFiltersDrawer: React.FC<RAMoreFiltersDrawerProps> = ({
  filters,
  onFilterChange,
  optionsData: {vessels, vesselCategories, offices},
}) => {
  const [localFilters, setLocalFilters] = useState<RAFilterValues>(filters);

  const [visibleOptionalFilterKeys, setVisibleOptionalFilterKeys] =
    React.useState<string[]>([]);

  // Sync local filters with parent filters
  useEffect(() => {
    setLocalFilters(filters);

    // Initialize visible optional filter keys based on current filter values
    const optionalKeys: string[] = [];
    if (filters.approval_date) {
      optionalKeys.push('approval_date');
    }
    if (filters.assessment_date) {
      optionalKeys.push('assessment_date');
    }
    setVisibleOptionalFilterKeys(optionalKeys);
  }, [filters]);

  // Count active filters (excluding search)
  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (filters.approval_status?.length > 0) count++;
    if (
      (filters.vessel_or_office?.vessel_id?.length ?? 0) > 0 ||
      (filters.vessel_or_office?.office_id?.length ?? 0) > 0
    )
      count++;
    if (filters.vessel_category?.length > 0) count++;
    if (filters.ra_level?.length > 0) count++;
    if (filters.submitted_on) count++;
    if (filters.approval_date) count++;
    if (filters.assessment_date) count++;
    if (filters.sync_from_paris_one) count++;
    return count;
  }, [filters]);

  const handleFilterChange: RAFiltersProps['onFilterChange'] = useCallback(
    (key, value) => {
      setLocalFilters(prev => ({...prev, [key]: value}));
    },
    [],
  );

  const primaryFilterConfig = useMemo(
    () =>
      getRaBasicFiltersFormConfig(
        {
          filters: localFilters,
          onFilterChange: handleFilterChange,
        },
        {vessels, vesselCategories, offices},
      ),
    [localFilters, handleFilterChange, vessels, vesselCategories, offices],
  );

  const optionalFilterConfig = useMemo(
    () =>
      getRaMoreFiltersFormConfig({
        filters: localFilters,
        onFilterChange: handleFilterChange,
      }),
    [localFilters, handleFilterChange],
  );

  const visibleOptionalFilters = optionalFilterConfig.filter(({key}) =>
    visibleOptionalFilterKeys.includes(key),
  );

  const removeOptionalFilter = (
    key: keyof Pick<RAFilterValues, 'approval_date' | 'assessment_date'>,
  ) => {
    setVisibleOptionalFilterKeys(prev => prev.filter(item => item !== key));
    handleFilterChange(key, null);
  };

  const handleClearFilters = () => {
    setLocalFilters(raFiltersInitialState);
    setVisibleOptionalFilterKeys([]);
    onFilterChange('approval_status', []);
    onFilterChange('vessel_or_office', null);
    onFilterChange('vessel_category', []);
    onFilterChange('ra_level', []);
    onFilterChange('submitted_on', null);
    onFilterChange('approval_date', null);
    onFilterChange('assessment_date', null);
    onFilterChange('sync_from_paris_one', false);
  };

  const handleUpdateFilters = (filters: RAFilterValues) => {
    onFilterChange('approval_date', filters.approval_date || null);
    onFilterChange('assessment_date', filters.assessment_date || null);
    onFilterChange('approval_status', filters.approval_status || null);
    onFilterChange('vessel_or_office', filters.vessel_or_office || null);
    onFilterChange('vessel_category', filters.vessel_category || []);
    onFilterChange('ra_level', filters.ra_level || null);
    onFilterChange('submitted_on', filters.submitted_on || null);
    onFilterChange('sync_from_paris_one', filters.sync_from_paris_one || false);
  };

  return (
    <Drawer
      trigger={
        <Button variant="outline-primary" className="more-filters-button">
          <PlusIcon className="icon" />
          {activeFiltersCount > 0 ? (
            <span className="label">
              Filters <span className="label-badge">{activeFiltersCount}</span>
            </span>
          ) : (
            <span className="label">More Filters</span>
          )}
        </Button>
      }
    >
      {(props: {closeDrawer: () => void}) => (
        <>
          <div className="add-more-filter-header">
            <div>All Filters</div>
            <Dropdown className="add-more-filter-dropdown-container">
              <Dropdown.Toggle
                as="div"
                className="add-more-filter-dropdown-toggle"
              >
                <PlusIcon className="dropdown-icon" />{' '}
                <span className="dropdown-label">Add More Filters</span>
              </Dropdown.Toggle>
              <Dropdown.Menu
                popperConfig={{
                  modifiers: [
                    {name: 'preventOverflow', options: {boundary: 'viewport'}},
                  ],
                }}
              >
                {primaryFilterConfig.map(({key, label}) => (
                  <Dropdown.Item key={key} disabled>
                    {label}
                  </Dropdown.Item>
                ))}
                {optionalFilterConfig.map(({key, label}) => (
                  <Dropdown.Item
                    key={key}
                    onClick={() => {
                      setVisibleOptionalFilterKeys(prev => {
                        if (prev.includes(key)) {
                          return prev.filter(item => item !== key);
                        } else {
                          return [...prev, key];
                        }
                      });
                      handleFilterChange(key, null);
                    }}
                  >
                    {label}
                  </Dropdown.Item>
                ))}
              </Dropdown.Menu>
            </Dropdown>
          </div>

          <main className="ra-more-filters-content">
            <div className="filter-container">
              {primaryFilterConfig
                .filter(config => config.key !== 'search') // Exclude search from more filters
                .map(({key, label, component}) => {
                  return (
                    <div key={key}>
                      <p className="filter-label">{label}</p>
                      <Form.Group className="filter-input" controlId={key}>
                        {component}
                      </Form.Group>
                      {/* Add checkbox after Submitted on */}
                      {key === 'submission_date' && (
                        <>
                          <hr />
                          <div className="checkbox-margin">
                            <CheckboxComponent
                              id="show-migrated-records"
                              checked={
                                localFilters.sync_from_paris_one || false
                              }
                              label="Show Migrated Records from PARIS 1.0"
                              onChange={() => {
                                handleFilterChange(
                                  'sync_from_paris_one',
                                  !localFilters.sync_from_paris_one,
                                );
                              }}
                            />
                          </div>
                        </>
                      )}
                    </div>
                  );
                })}

              {visibleOptionalFilters.length > 0 && (
                <div className="filters-divider" />
              )}

              {visibleOptionalFilters.map(({key, label, component}) => {
                return (
                  <div key={key}>
                    <div className="d-flex justify-content-between align-items-center">
                      <p className="filter-label">{label}</p>
                      <button
                        className="ra-no-style-btn"
                        onClick={() => removeOptionalFilter(key)}
                      >
                        <TrashIcon />
                      </button>
                    </div>
                    <Form.Group className="filter-input" controlId={key}>
                      {component}
                    </Form.Group>
                  </div>
                );
              })}
            </div>
            <div className="filters-footer">
              <Button
                variant="link"
                className="footer-btn-secondary"
                onClick={() => {
                  handleClearFilters();
                  props.closeDrawer();
                }}
              >
                Clear
              </Button>
              <Button
                variant="primary"
                className="footer-btn-primary"
                onClick={() => {
                  handleUpdateFilters(localFilters);
                  props.closeDrawer();
                }}
              >
                Apply
              </Button>
            </div>
          </main>
        </>
      )}
    </Drawer>
  );
};

export default RAMoreFiltersDrawer;

const getRaMoreFiltersFormConfig = (raFilterProps: {
  filters: Pick<RAFilterValues, 'approval_date' | 'assessment_date'>;
  onFilterChange: (
    key: keyof Pick<RAFilterValues, 'approval_date' | 'assessment_date'>,
    value: DateRangeValue,
  ) => void;
}) => {
  const {filters, onFilterChange} = raFilterProps;
  return [
    {
      key: 'assessment_date' as const,
      label: 'Date of Assessment',
      component: (
        <CustomDatePickerWithRange
          controlId="ra_filters_assessment_date"
          placeholder="Date of Assessment"
          startDate={
            filters.assessment_date?.[0]
              ? new Date(filters.assessment_date?.[0])
              : undefined
          }
          endDate={
            filters.assessment_date?.[1]
              ? new Date(filters.assessment_date?.[1])
              : undefined
          }
          onChange={([start, end]) =>
            onFilterChange('assessment_date', [start ?? null, end ?? null])
          }
        />
      ),
    },
    {
      key: 'approval_date' as const,
      label: 'Approval Date Range',
      component: (
        <CustomDatePickerWithRange
          controlId="ra_filters_approval_date"
          placeholder="Approval Date Range"
          startDate={
            filters.approval_date?.[0]
              ? new Date(filters.approval_date?.[0])
              : undefined
          }
          endDate={
            filters.approval_date?.[1]
              ? new Date(filters.approval_date?.[1])
              : undefined
          }
          onChange={([start, end]) =>
            onFilterChange('approval_date', [start ?? null, end ?? null])
          }
        />
      ),
    },
  ];
};
