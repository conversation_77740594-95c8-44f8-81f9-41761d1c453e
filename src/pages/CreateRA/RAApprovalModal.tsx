import React, {useState} from 'react';
import {<PERSON><PERSON>, But<PERSON>} from 'react-bootstrap';
import {toast} from 'react-toastify';
import CustomDatePicker from '../../components/CustomDatePicker';
import {InputComponent} from '../../components/InputComponent';
import {ApprovalStatus} from '../../enums';
import {getErrorMessage} from '../../utils/common';

import '../../styles/components/re-assign-approver-modal.scss';

export type ApprovalOperationType = 'approve' | 'approveWithComment' | 'reject';

export const operationTypeToApprovalStatus: Record<
  ApprovalOperationType,
  ApprovalStatus
> = {
  approve: ApprovalStatus.APPROVED,
  approveWithComment: ApprovalStatus.CONDITIONALLY_APPROVED,
  reject: ApprovalStatus.REJECTED,
};

export interface RAApprovalModalProps {
  onConfirm: (params: {
    operationType: ApprovalOperationType;
    actionDate?: Date;
    comment?: string;
  }) => Promise<{message?: string} | void>;
  operationType?: ApprovalOperationType;
  trigger: React.ReactElement;
  reviewIndex?: number;
}

const RAApprovalModal: React.FC<RAApprovalModalProps> = ({
  onConfirm,
  operationType = 'approve',
  trigger,
  reviewIndex = 0,
}) => {
  const [show, setShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isTouched, setIsTouched] = useState(false);
  const [actionDate, setActionDate] = useState<Date | undefined>(undefined);
  const [comment, setComment] = useState<string>('');

  const handleTriggerClick = () => {
    setShow(true);
  };

  const handleClose = () => {
    setShow(false);
    setActionDate(undefined);
    setIsTouched(false);
    setComment('');
    setIsLoading(false);
  };

  const handleConfirm = async () => {
    try {
      setIsLoading(true);
      await onConfirm({
        operationType,
        actionDate,
        comment: comment.trim(),
      });
      if (operationType === 'reject') {
        toast.success('RA Rejected');
      } else if (operationType === 'approve') {
        toast.success('RA Approved');
      } else {
        toast.success('RA Approved with Condition');
      }
    } catch (error) {
      toast.error(
        getErrorMessage(
          error,
          'Failed to approve the RA. Please try again later.',
        ),
      );
    } finally {
      setIsLoading(false);
    }

    handleClose();
  };

  let disableButtons: boolean =
    operationType === 'approve' ? !actionDate : false;
  if (operationType === 'approveWithComment') {
    disableButtons = !actionDate || !comment.trim();
  }
  if (operationType === 'reject') {
    disableButtons = !actionDate || !comment.trim();
  }

  const handelDateChange = (date: Date | undefined) => {
    setActionDate(date);
    setIsTouched(true);
  };

  return (
    <>
      {trigger &&
        React.cloneElement(trigger, {
          onClick: handleTriggerClick,
        })}

      <Modal
        show={show}
        onHide={handleClose}
        size="lg"
        backdrop="static"
        className="reassign-approver-modal"
        dialogClassName="top-modal ra-approval-exit-modal"
      >
        <Modal.Header>
          <Modal.Title className="fs-20">
            {operationType === 'reject' ? 'Rejecting' : 'Approving'} Risk
            Assessment{' '}
            {operationType === 'approveWithComment' ? 'with Condition' : ''}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {operationType === 'reject' && (
            <div className="text-warning red-text-warning">
              <span className="fw-600">
                Do you really want to Reject? This action is not reversible.
              </span>
              <br />
              Once confirmed, the Risk Assessment will be marked as Rejected and
              no further changes will be allowed
            </div>
          )}

          {(operationType === 'approve' ||
            operationType === 'approveWithComment') &&
            reviewIndex === 0 && (
              <div className="text-warning yellow-text-warning">
                <span className="fw-600">
                  Do you really want to Approve the RA? This action is not
                  revertible.
                </span>{' '}
                After your approval, the Second Reviewer will be notified for
                Approval.
              </div>
            )}
          {(operationType === 'approve' ||
            operationType === 'approveWithComment') &&
            reviewIndex === 1 && (
              <div className="text-warning yellow-text-warning">
                <span className="fw-600">
                  Do you really want to Approve the RA? This action is not
                  revertible.
                </span>{' '}
                After your approval, the Final Approver will be notified for
                Approval.
              </div>
            )}

          {operationType === 'approve' && reviewIndex === 2 && (
            <div className="text-warning yellow-text-warning">
              <span className="fw-600">
                Do you really want to give Final Approval? This action is not
                reversible.
              </span>
              <br />
              Once confirmed, the Risk Assessment will be marked as Approved and
              no further changes will be allowed
            </div>
          )}

          {operationType === 'approveWithComment' && reviewIndex === 2 && (
            <div className="text-warning yellow-text-warning">
              <span className="fw-600">
                Do you really want to give Final Approval with Condition? This
                action is not reversible.
              </span>
              <br />
              Once confirmed, the Risk Assessment will be marked as Approved
              with Condition and no further changes will be allowed
            </div>
          )}

          {operationType === 'reject' ? (
            <>
              <CustomDatePicker
                isRequired={true}
                minDate={undefined}
                label="Rejection Date"
                value={actionDate ?? undefined}
                onChange={handelDateChange}
                onBlur={() => setIsTouched(true)}
                placeholder="Select Date"
                controlId="rejection_date"
                errorMsg={
                  isTouched
                    ? 'This is a mandatory field. Please fill to process.'
                    : undefined
                }
                className="fs-14 mb-3"
              />
              <InputComponent
                label="Reason for Rejecting RA"
                type="textarea"
                onChange={(
                  e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
                ) => {
                  setComment(e.target.value);
                }}
                maxLength={255}
                rows={4}
                showMaxLength
                value={comment}
                name="reason_for_rejection"
                placeholder="Type the Reason for Rejection"
                className="fs-14"
              />
            </>
          ) : (
            <>
              <CustomDatePicker
                isRequired={true}
                minDate={undefined}
                label="Approval Date"
                value={actionDate ?? undefined}
                onChange={handelDateChange}
                onBlur={() => setIsTouched(true)}
                placeholder="Select Date"
                controlId="approval_date"
                errorMsg={
                  isTouched
                    ? 'This is a mandatory field. Please fill to process.'
                    : undefined
                }
                className={
                  operationType === 'approveWithComment'
                    ? 'fs-14 mb-3'
                    : 'fs-14'
                }
              />

              {operationType === 'approveWithComment' && (
                <InputComponent
                  label="Condition for Approval"
                  type="textarea"
                  onChange={(
                    e: React.ChangeEvent<
                      HTMLInputElement | HTMLTextAreaElement
                    >,
                  ) => {
                    setComment(e.target.value);
                  }}
                  maxLength={255}
                  rows={4}
                  showMaxLength
                  value={comment}
                  name="condition_for_approval"
                  placeholder="Type the Condition for Approval"
                  className="fs-14"
                />
              )}
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="primary"
            className="me-2 fs-14"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="secondary"
            className="me-2 fs-14 primary-btn"
            onClick={handleConfirm}
            disabled={isLoading || disableButtons}
          >
            {operationType === 'reject' ? 'Reject' : 'Approve'}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default RAApprovalModal;
