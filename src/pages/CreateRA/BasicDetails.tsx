import React, {
  useState,
  forwardRef,
  useImper<PERSON><PERSON><PERSON><PERSON>,
  useEffect,
} from 'react';
import {Col, Form, Row} from 'react-bootstrap';
import {InputComponent} from '../../components/InputComponent';
import {TemplateForm} from '../../types/template';
import {RiskForm} from '../../types';
import DropdownTypeahead from '../../components/DropdownTypeahead';
import SingleVesselOfficeDropdown from '../../components/SingleVesselOfficeDropdown';
import CustomDatePicker from '../../components/CustomDatePicker';
import {
  formatDateToYYYYMMDD,
  createGroupedVesselOfficeOptions,
  findSelectedVesselOfficeOption,
  SingleVesselOfficeOption,
  GroupedVesselOfficeOption,
} from '../../utils/helper';
import {useDataStoreContext} from '../../context';
import {vesselStatusAndLabelName} from '../../utils/common';
import {RACategoryRiskFormOption} from '../../constants/template';
import FormCheckRadio from '../../components/FormCheckRadio';

import '../../styles/components/basic-details.scss';

const errorMsg = 'This is a mandatory field. Please fill to process.';

const REQUIRED_FIELDS = [
  'task_requiring_ra',
  'task_duration',
  'task_alternative_consideration',
  'task_rejection_reason',
] as const;

const RISK_REQUIRED_FIELDS = [
  'task_requiring_ra',
  'task_duration',
  'task_alternative_consideration',
  'task_rejection_reason',
  'assessor',
  'date_risk_assessment',
  'approval_required',
] as const;

export const assessorOptions = [
  {value: 1, label: 'Office'},
  {value: 2, label: 'Vessel'},
];

// --- Utility Functions ---
const getFieldValue = (
  form: TemplateForm | RiskForm,
  fieldName: string,
): string => {
  const value = form[fieldName as keyof (TemplateForm | RiskForm)];
  return fieldName === 'task_duration'
    ? value?.toString() || ''
    : (value as string) || '';
};

const isFieldEmpty = (
  form: TemplateForm | RiskForm,
  fieldName: string,
): boolean => {
  if (fieldName === 'assessor' || fieldName === 'vessel_ownership_id') {
    const riskForm = form as RiskForm;
    const value = riskForm[fieldName as keyof RiskForm];
    return !value || (typeof value === 'number' && value === 0);
  }
  if (fieldName === 'approval_required') {
    const riskForm = form as RiskForm;
    const value = riskForm.approval_required;
    return !value || (Array.isArray(value) && value.length === 0);
  }
  const value = getFieldValue(form, fieldName);
  return !value || value?.trim() === '';
};

const validateAllFields = (
  form: TemplateForm | RiskForm,
  type: string,
): {[key: string]: string} => {
  const errors: {[key: string]: string} = {};
  const fieldsToValidate =
    type === 'risk' ? RISK_REQUIRED_FIELDS : REQUIRED_FIELDS;

  fieldsToValidate.forEach(field => {
    if (isFieldEmpty(form, field)) {
      errors[field] = errorMsg;
    }
  });

  if (type === 'risk') {
    const hasOfficeId =
      !!(form as RiskForm).office_id || (form as RiskForm).office_id === 0;
    const hasOwnershipId =
      !!(form as RiskForm).vessel_ownership_id ||
      (form as RiskForm).vessel_ownership_id === 0;

    if (!hasOfficeId && !hasOwnershipId) {
      errors.office_id = errorMsg;
      errors.vessel_ownership_id = errorMsg;
    }
  }

  return errors;
};

const validateSingleField = (
  form: TemplateForm | RiskForm,
  fieldName: string,
): string => {
  return isFieldEmpty(form, fieldName) ? errorMsg : '';
};

// --- Subcomponents ---
const NoteBox = () => (
  <div className="ra-basic-details-note">
    <span>
      <strong>Note:</strong> If the defined job duration exceeds, office must be
      notified for any extension or alternate action.
    </span>
  </div>
);

const RiskFields: React.FC<{
  form: RiskForm;
  touched: {[key: string]: boolean};
  validationErrors: {[key: string]: string};
  assessorOptions: {value: number; label: string}[];
  groupedVesselOfficeOptions: GroupedVesselOfficeOption[];
  approvalOptionsOffice: {value: number; label: string}[];
  approvalOptionsVessel: {value: number; label: string}[];
  handleAssessorChange: (selected: any) => void;
  handleVesselOfficeChange: (selected: any) => void;
  handleDateChange: (name: string, date?: Date) => void;
  handleApprovalChange: (selected: any) => void;
  setTouched: React.Dispatch<React.SetStateAction<{[key: string]: boolean}>>;
}> = ({
  form,
  touched,
  validationErrors,
  assessorOptions,
  groupedVesselOfficeOptions,
  approvalOptionsOffice,
  approvalOptionsVessel,
  handleAssessorChange,
  handleVesselOfficeChange,
  handleDateChange,
  handleApprovalChange,
  setTouched,
}) => (
  <>
    <Row className="mb-3">
      <Col md={4}>
        <DropdownTypeahead
          label="Assessor"
          options={assessorOptions}
          selected={
            assessorOptions.find(
              opt => opt.value === (form as RiskForm).assessor,
            ) || null
          }
          onChange={handleAssessorChange}
          onInputChange={() => setTouched(prev => ({...prev, assessor: true}))}
          onBlur={() => setTouched(prev => ({...prev, assessor: true}))}
          isInvalid={touched.assessor && !!validationErrors.assessor}
          errorMessage={validationErrors.assessor || errorMsg}
        />
      </Col>

      <Col md={5}>
        <SingleVesselOfficeDropdown
          label="Vessel/Office"
          options={groupedVesselOfficeOptions}
          value={findSelectedVesselOfficeOption(
            groupedVesselOfficeOptions,
            (form as RiskForm).office_id
              ? (form as RiskForm).office_id
              : (form as RiskForm).vessel_ownership_id,
          )}
          placeholder={`Search & Select ${
            (form as RiskForm).assessor === 1 ? 'Office/Vessel' : 'Vessel'
          }`}
          onChange={handleVesselOfficeChange}
          onBlur={() =>
            setTouched(prev => ({...prev, vessel_ownership_id: true}))
          }
          isInvalid={
            touched.vessel_ownership_id &&
            !!validationErrors.vessel_ownership_id
          }
          errorMessage={validationErrors.vessel_ownership_id || errorMsg}
        />
      </Col>
    </Row>
    <Row className="mb-3">
      <Col md={4}>
        <CustomDatePicker
          label="Date of Risk Assessment"
          value={
            (form as RiskForm).date_risk_assessment
              ? new Date((form as RiskForm).date_risk_assessment)
              : undefined
          }
          onChange={date => handleDateChange('date_risk_assessment', date)}
          placeholder="Select Date"
          controlId="date_risk_assessment"
          isRequired={true}
          errorMsg={
            touched.date_risk_assessment
              ? validationErrors.date_risk_assessment
              : ''
          }
          minDate={undefined}
        />
      </Col>
      <Col md={5}>
        <DropdownTypeahead
          label="On Vessel Approvals (Optional)"
          options={
            (form as RiskForm).assessor === 2
              ? approvalOptionsVessel
              : approvalOptionsOffice
          }
          selected={
            (form as RiskForm).assessor === 2
              ? approvalOptionsVessel.filter(opt =>
                  (form as RiskForm).approval_required?.includes(opt.value),
                )
              : approvalOptionsOffice.filter(opt =>
                  (form as RiskForm).approval_required?.includes(opt.value),
                )
          }
          onChange={handleApprovalChange}
          multiple
          onInputChange={() =>
            setTouched(prev => ({...prev, approval_required: true}))
          }
          onBlur={() =>
            setTouched(prev => ({...prev, approval_required: true}))
          }
          isInvalid={
            touched.approval_required && !!validationErrors.approval_required
          }
          errorMessage={validationErrors.approval_required || errorMsg}
          useCheckboxes
        />
      </Col>
    </Row>
  </>
);

// --- Handlers as Hooks ---
function useFormHandlers({
  form,
  setForm,
  setTouched,
  setValidationErrors,
  validate,
}: {
  form: TemplateForm | RiskForm;
  setForm: any;
  setTouched: React.Dispatch<React.SetStateAction<{[key: string]: boolean}>>;
  setValidationErrors: React.Dispatch<
    React.SetStateAction<{[key: string]: string}>
  >;
  validate: (customForm?: TemplateForm | RiskForm) => boolean;
}) {
  const handleChange = (e: any) => {
    const {name, value} = e.target;
    const updatedForm = {...form, [name]: value};
    setForm(updatedForm);
    setTouched(prev => ({...prev, [name]: true}));
    const fieldError = validateSingleField(updatedForm, name);
    setValidationErrors(prev => ({...prev, [name]: fieldError}));
    validate(updatedForm);
  };

  const handleDropdownChange = (name: string, value: any) => {
    const updatedForm = {...form, [name]: value};
    setForm(updatedForm);
    setTouched(prev => ({...prev, [name]: true}));
    const fieldError = validateSingleField(updatedForm, name);
    setValidationErrors(prev => ({...prev, [name]: fieldError}));
    validate(updatedForm);
  };

  const handleDateChange = (name: string, date?: Date) => {
    setTouched(prev => ({...prev, [name]: true}));
    const updatedForm = {
      ...form,
      [name]: date ? formatDateToYYYYMMDD(date) : '',
    };
    setForm(updatedForm);
    const fieldError = validateSingleField(updatedForm, name);
    setValidationErrors(prev => ({...prev, [name]: fieldError}));
    validate(updatedForm);
  };

  const handleAssessorChange = (selected: any) => {
    const value = Array.isArray(selected)
      ? selected[0]?.value
      : selected?.value;
    setTouched(prev => ({...prev, assessor: true}));

    // Clear risk_team_member when assessor changes for risk forms
    // Also clear vessel/office selection when assessor changes
    const updatedForm = {
      ...form,
      assessor: value || 0,
      ...(Object.prototype.hasOwnProperty.call(form, 'risk_team_member') && {
        risk_team_member: [],
      }),
      ...(Object.prototype.hasOwnProperty.call(form, 'approval_required') && {
        approval_required: [],
      }),
      // Clear office if assessor is Vessel
      ...(value === 2 && {
        office_id: 0,
        office_name: '',
      }),
      // Clear vessel if assessor is Office
      ...(value === 1 && {
        vessel_ownership_id: 0,
      }),
    };
    setForm(updatedForm);

    const fieldError = validateSingleField(updatedForm, 'assessor');
    setValidationErrors(prev => ({...prev, assessor: fieldError}));
    validate(updatedForm);
  };

  // Handle vessel/office dropdown change
  const handleVesselOfficeChange = (
    selected: SingleVesselOfficeOption | null,
  ) => {
    const value = selected?.value ? Number(selected.value) : 0;
    const vesselId = selected?.vesselId;

    setTouched(prev => ({...prev, vessel_ownership_id: true}));

    // Update both vessel_ownership_id and vessel_id for risk forms
    const updatedForm = {
      ...form,
      ...(vesselId
        ? {
            vessel_ownership_id: value,
            vessel_id: vesselId,
            office_id: 0,
            office_name: '',
          }
        : {
            office_id: value,
            office_name: selected?.label ?? '',
            vessel_ownership_id: 0,
            vessel_id: 0,
          }),
      // Clear risk_team_member when vessel/office changes for risk forms
      ...(Object.prototype.hasOwnProperty.call(form, 'risk_team_member') && {
        risk_team_member: [],
      }),
    };
    setForm(updatedForm);

    const fieldError = !value ? errorMsg : '';
    setValidationErrors(prev => ({...prev, vessel_ownership_id: fieldError}));
    validate(updatedForm);
  };

  const handleApprovalChange = (selected: any) => {
    let values: any[] = [];
    if (Array.isArray(selected)) {
      values = selected.map(item => item.value);
    } else if (selected) {
      values = [selected.value];
    }
    const updatedForm = {...form, approval_required: values};
    setForm(updatedForm);
    setTouched(prev => ({...prev, approval_required: true}));
    const fieldError = validateSingleField(updatedForm, 'approval_required');
    setValidationErrors(prev => ({
      ...prev,
      approval_required: fieldError,
    }));
    validate(updatedForm);
  };

  const handleBlur = (e: any) => {
    const {name} = e.target;
    setTouched(prev => ({...prev, [name]: true}));
    const fieldError = validateSingleField(form, name);
    setValidationErrors(prev => ({...prev, [name]: fieldError}));
    validate();
  };

  return {
    handleChange,
    handleDropdownChange,
    handleDateChange,
    handleAssessorChange,
    handleVesselOfficeChange,
    handleApprovalChange,
    handleBlur,
  };
}

// --- Option Loaders ---
function useOptions(type: string, assessor?: number) {
  const {
    dataStore: {
      vesselListForRisk,
      officeListForRisk,
      approversReqListForRiskOffice,
      approversReqListForRiskVessel,
    },
  } = useDataStoreContext();

  const [approvalOptionsOffice, setApprovalOptionsOffice] = useState<
    {value: number; label: string}[]
  >([]);
  const [approvalOptionsVessel, setApprovalOptionsVessel] = useState<
    {value: number; label: string}[]
  >([]);
  const [groupedVesselOfficeOptions, setGroupedVesselOfficeOptions] = useState<
    GroupedVesselOfficeOption[]
  >([]);

  useEffect(() => {
    if (type === 'risk') {
      const loadOptions = async () => {
        try {
          // Create grouped options using helper function
          const allGroupedOptions = createGroupedVesselOfficeOptions(
            vesselListForRisk,
            officeListForRisk,
            vesselStatusAndLabelName,
          );

          // Filter options based on assessor selection
          let filteredOptions = allGroupedOptions;
          if (assessor === 2) {
            // When assessor is Vessel, only show vessel groups (exclude Offices)
            filteredOptions = allGroupedOptions.filter(
              group => group.label !== 'Offices',
            );
          }

          setGroupedVesselOfficeOptions(filteredOptions);

          setApprovalOptionsOffice(
            approversReqListForRiskOffice.map(item => ({
              value: item.id,
              label: item.name,
            })),
          );

          setApprovalOptionsVessel(
            approversReqListForRiskVessel.map(item => ({
              value: item.id,
              label: item.name,
            })),
          );
        } catch (error) {
          console.error('Error loading vessel/office options:', error);
        }
      };

      loadOptions();
    }
  }, [
    type,
    vesselListForRisk,
    officeListForRisk,
    approversReqListForRiskOffice,
    approversReqListForRiskVessel,
    assessor,
  ]);

  return {
    approvalOptionsOffice,
    approvalOptionsVessel,
    groupedVesselOfficeOptions,
  };
}

// Outside component
const isLevel1Available = (form: RiskForm, canAddRALvl1: boolean) => {
  return form?.assessor === 1 && canAddRALvl1;
};

const shouldSwitchToRegularRA = (form: RiskForm, canAddRALvl1: boolean) => {
  return form?.ra_level === 4 && !isLevel1Available(form, canAddRALvl1);
};

const needsDefaultRA = (form: RiskForm) => {
  return form?.ra_level === undefined || form?.ra_level === null;
};

// --- Main Component ---
const BasicDetails = forwardRef(
  (
    {
      form,
      setForm,
      onValidate,
      type = 'template',
    }: {
      form: TemplateForm | RiskForm;
      setForm: any;
      onValidate?: (valid: boolean) => void;
      type?: string;
    },
    ref,
  ) => {
    const [validationErrors, setValidationErrors] = useState<{
      [key: string]: string;
    }>({});
    const [touched, setTouched] = useState<{[key: string]: boolean}>({});
    const {
      roleConfig: {
        riskAssessment: {canAddRALvl1},
      },
    } = useDataStoreContext();
    const {
      approvalOptionsOffice,
      approvalOptionsVessel,
      groupedVesselOfficeOptions,
    } = useOptions(type, (form as RiskForm)?.assessor);

    // --- Validation ---
    const validate = (customForm?: TemplateForm | RiskForm) => {
      const f = customForm || form;
      const errors = validateAllFields(f, type);
      const isValid = Object.keys(errors).length === 0;
      if (onValidate) onValidate(isValid);
      setValidationErrors(prev => ({...prev, ...errors}));
      return isValid;
    };

    // --- Handlers ---
    const {
      handleChange,
      handleDateChange,
      handleAssessorChange,
      handleVesselOfficeChange,
      handleApprovalChange,
      handleBlur,
    } = useFormHandlers({
      form,
      setForm,
      setTouched,
      setValidationErrors,
      validate,
    });

    useImperativeHandle(ref, () => ({
      validate,
    }));

    useEffect(() => {
      if (type !== 'risk') return;

      const riskForm = form as RiskForm;

      if (shouldSwitchToRegularRA(riskForm, canAddRALvl1)) {
        setForm((prev: any) => ({
          ...prev,
          ra_level: undefined,
        }));
      }

      if (needsDefaultRA(riskForm)) {
        setForm((prev: any) => ({
          ...prev,
          ra_level: undefined,
        }));
      }
    }, [(form as RiskForm)?.assessor, canAddRALvl1, type]);

    // --- Render ---
    return (
      <Col className="ra-negate-padding">
        <div className="mb-3 ra-basic-details-title">
          Enter Basic RA Details
        </div>
        <Form className="ra-negate-padding">
          <Col md={9} className="ra-negate-padding">
            <InputComponent
              label="Task Requiring R.A."
              name="task_requiring_ra"
              value={form.task_requiring_ra}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="Enter the task requiring risk assessment"
              type="text"
              maxLength={255}
              formControlId="task_requiring_ra"
              form={form}
              error={
                touched.task_requiring_ra
                  ? validationErrors.task_requiring_ra
                  : ''
              }
            />
          </Col>
          {type === 'risk' && (
            <RiskFields
              form={form as RiskForm}
              touched={touched}
              validationErrors={validationErrors}
              assessorOptions={assessorOptions}
              groupedVesselOfficeOptions={groupedVesselOfficeOptions}
              approvalOptionsOffice={approvalOptionsOffice}
              approvalOptionsVessel={approvalOptionsVessel}
              handleAssessorChange={handleAssessorChange}
              handleVesselOfficeChange={handleVesselOfficeChange}
              handleDateChange={handleDateChange}
              handleApprovalChange={handleApprovalChange}
              setTouched={setTouched}
            />
          )}
          <Row className="mb-3">
            <Col md={4}>
              <InputComponent
                label="Duration of Task"
                name="task_duration"
                value={`${form.task_duration}`}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder="Enter No. of Days Required"
                type="text"
                formControlId="task_duration"
                maxLength={2500}
                form={form}
                helpText="Mention if the values are in Days/Hours"
                error={
                  touched.task_duration ? validationErrors.task_duration : ''
                }
              />
            </Col>
            <Col md={5}>
              {canAddRALvl1 &&
                type === 'risk' &&
                (form as RiskForm)?.assessor === 1 && (
                  <Form.Group className="mb-3" key="ra-category">
                    <Form.Label className="ra-basic-details-label">
                      RA Category
                    </Form.Label>
                    <div className="ra-basic-details-radio-group">
                      {RACategoryRiskFormOption.filter(({value}) => {
                        // Always show Regular RA (value: undefined)
                        if (value === undefined) return true;
                        if (
                          (!(form as RiskForm)?.office_id ||
                            (form as RiskForm)?.template_id) &&
                          value === 4
                        )
                          return false;
                        if (!(form as RiskForm)?.office_id && value === 4)
                          return false;
                        // Show Level 1 RA only if assessor is Office
                        return (form as RiskForm)?.assessor === 1;
                      }).map(({label, value}) => (
                        <FormCheckRadio
                          key={label}
                          checked={(form as RiskForm)?.ra_level === value}
                          onChange={() =>
                            setForm((prev: any) => ({
                              ...prev,
                              ra_level: value,
                            }))
                          }
                          label={label}
                          name="ra_category"
                          value={value}
                          className="me-2 fs-14 fw-400"
                          id={`ra_category-select-${value}`}
                          disabled={false}
                        />
                      ))}
                    </div>
                  </Form.Group>
                )}
            </Col>
          </Row>
          <Col md={9} className="ra-negate-padding">
            <NoteBox />
          </Col>
          <hr />
          <Col md={9} className="ra-negate-padding">
            <InputComponent
              label="Alternative Considered to carry out above task"
              name="task_alternative_consideration"
              value={form.task_alternative_consideration}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="List the Alternatives Considered for the Task"
              type="textarea"
              formControlId="task_alternative_consideration"
              form={form}
              maxLength={2500}
              showMaxLength
              rows={3}
              error={
                touched.task_alternative_consideration
                  ? validationErrors.task_alternative_consideration
                  : ''
              }
              classes={{
                label: 'fs-16 fw-600',
              }}
            />
            <InputComponent
              label="Reason for Rejecting Alternatives"
              name="task_rejection_reason"
              value={form.task_rejection_reason}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="List the Reasons for Rejecting the Alternatives"
              type="textarea"
              formControlId="task_rejection_reason"
              form={form}
              showMaxLength
              maxLength={2500}
              rows={3}
              error={
                touched.task_rejection_reason
                  ? validationErrors.task_rejection_reason
                  : ''
              }
              classes={{
                label: 'fs-16 fw-600',
              }}
            />
          </Col>
        </Form>
      </Col>
    );
  },
);

BasicDetails.displayName = 'BasicDetails';

export {BasicDetails};
