import React from 'react';
import classNames from 'classnames';
import {toast} from 'react-toastify';
import TruncateText from '../../../components/TruncateBasicText';
import SingleBadgePopover from '../../../components/SingleBadgePopover';
import {parseDate} from '../../../utils/common';
import {useQuery} from '../../../hooks/useQuery';
import {getMostlyUsedTemplates} from '../../../services/services';
import {getInitials} from '../../../utils/user';
import {Template} from '../../../types/template';
import {
  ActionDropdownMenu,
  ActionDropdownMenuProps,
} from './ActionDropdownMenu';
import Popover from '../../../components/Popover';

import '../../../styles/components/mostly-used-card.scss';

export const MostlyUsedCardList: React.FC<
  Pick<MostlyUsedCardProps, 'hideMenu' | 'extraFooterOptions' | 'onClick'> & {
    selectedCardId?: number;
  }
> = props => {
  const {data, isLoading} = useQuery(
    ['mostly-used-templates'],
    getMostlyUsedTemplates,
    {onError: err => toast.error(err?.message || 'Something went wrong')},
  );

  if (isLoading) {
    return (
      <>
        <div className="mostly-used-title">Mostly Used</div>
        <div className="ra-mostly-used-cards">Loading...</div>
      </>
    );
  }

  if (!data || data?.results?.length === 0) return null;
  const {results: items, userDetails} = data;

  return (
    <>
      <div className="mostly-used-title">Mostly Used</div>
      <div className="ra-mostly-used-cards">
        {items.slice(0, 4).map(template => {
          const userName =
            userDetails.find(user => user.userId === template.created_by)
              ?.full_name || template.created_by;
          return (
            <MostlyUsedCard
              templateId={template.id}
              key={template.id}
              templateName={template.task_requiring_ra}
              riskCategories={template.template_category}
              hazardCategories={template.template_hazards}
              keywords={template.template_keywords}
              createdOn={template.created_at}
              userName={userName}
              menuAlign="vertical"
              isSelected={props.selectedCardId === template.id}
              {...props}
            />
          );
        })}
      </div>
    </>
  );
};

export interface MostlyUsedCardProps {
  templateId: number;
  templateName: string;
  riskCategories: Template['template_category'];
  hazardCategories: Template['template_hazards'];
  keywords: Template['template_keywords'];
  menuAlign?: ActionDropdownMenuProps['menuAlign'];
  createdOn: string;
  userName: string;
  className?: string;
  hideMenu?: boolean;
  extraFooterOptions?: React.ReactNode;
  onClick?: (templateId: number) => void;
  isSelected?: boolean;
}

export const MostlyUsedCard: React.FC<MostlyUsedCardProps> = ({
  templateId,
  templateName,
  riskCategories,
  hazardCategories,
  keywords,
  createdOn,
  userName,
  className,
  menuAlign,
  hideMenu = false,
  extraFooterOptions,
  onClick,
  isSelected = false,
}) => {
  return (
    <button
      className={classNames(
        'mostly-used-card',
        className,
        typeof onClick === 'function' && 'mostly-used-card-focus',
        isSelected && 'mostly-used-card-selected',
      )}
      onClick={() => onClick?.(templateId)}
    >
      <div className="card-header">
        <div className="template-name">
          <TruncateText text={templateName} maxLength={76} />
        </div>
        <div className="categories">
          <div>Risk Categories: {riskCategories.length}</div>
          <div>Hazard Categories: {hazardCategories.length}</div>
          <SingleBadgePopover
            items={keywords.map(keyword => keyword.name)}
            label={`Keywords: ${keywords.length}`}
          />
        </div>
      </div>
      <div className="card-footer">
        <div className="created-on">Created on: {parseDate(createdOn)}</div>
        <div className="user-avatar">
          <Popover text={userName}>
            <div className="avatar-circle">{getInitials(userName)}</div>
          </Popover>
          {extraFooterOptions || null}
          {!hideMenu && (
            <ActionDropdownMenu
              data={{
                id: templateId,
                template_category: riskCategories,
                template_hazards: hazardCategories,
                template_keywords: keywords,
                task_requiring_ra: templateName,
                createdAt: createdOn,
                created_at: createdOn,
              }}
              userDetails={{full_name: userName, email: ''}}
              menuAlign={menuAlign}
              showModal
            />
          )}
        </div>
      </div>
    </button>
  );
};
