import React, {useEffect, useMemo, useState} from 'react';
import {Row, Col, Form} from 'react-bootstrap';
import {toast} from 'react-toastify';
import SearchInput from '../../../components/SearchInput';
import DropdownTypeahead from '../../../components/DropdownTypeahead';
import {CategoriesFiltersDrawer} from '../../../components/CategoriesFiltersDrawer';
import {
  getHazardsList,
  getRiskCategoryList,
  getTemplateUserList,
} from '../../../services/services';
import {useDataStoreContext} from '../../../context';
import CustomDatePickerWithRange from '../../../components/CustomDatePickerWithRange';
import SearchUserDropdown from '../../../components/SearchUserDropdown';

import '../../../styles/components/template-listing-filters.scss';

export type FilterOption = {
  label: string;
  value: string | number;
  full_name?: string;
  designation?: string;
};

export interface TemplateFilterValues {
  search: string;
  created_by: string[] | null;
  created_at: [string, string] | null;
  ra_categories: number[] | null;
  hazard_categories?: number[] | null;
  template_category?: number[] | null;
}

interface TemplateListingFiltersProps {
  filters: TemplateFilterValues;
  onFilterChange: (
    key: keyof TemplateFilterValues,
    value: FilterOption | string | null | (string | null)[] | number[],
  ) => void;
}

export const TemplateListingFilters: React.FC<TemplateListingFiltersProps> = ({
  filters,
  onFilterChange,
}) => {
  const {setDataStore} = useDataStoreContext();
  const [templateUsers, setTemplateUsers] = useState<FilterOption[]>([]);

  useEffect(() => {
    const loadBasicDetails = async () => {
      try {
        const [categoryListData, hazardsListData, templateUserList] =
          await Promise.all([
            getRiskCategoryList(),
            getHazardsList(),
            getTemplateUserList(),
          ]);

        setDataStore(prev => ({
          ...prev,
          riskCategoryList: categoryListData,
          hazardsList: hazardsListData,
        }));
        setTemplateUsers(
          templateUserList.map(user => ({
            label: user.email,
            value: user.userId,
            full_name: user.full_name || user.email,
            designation: user.designation,
          })),
        );
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load data. Please try again later.');
      }
    };
    loadBasicDetails();
  }, []);
  const hideCategories = true;
  const filterConfig = useMemo(
    () => [
      {
        key: 'task_requiring_ra' as const,
        component: (
          <SearchInput
            value={filters.search}
            onSearch={value => onFilterChange('search', value)}
            placeholder="Search by Task Required or Keywords"
          />
        ),
      },
      {
        key: 'created_by' as const,
        component: (
          <SearchUserDropdown
            value={filters.created_by || []}
            options={templateUsers.map(user => ({
              id: user.value.toString(),
              full_name: String(user.full_name),
              designation: user.designation,
              email: user.label,
              role: user.label ?? 'Template User',
            }))}
            placeholder="Created by"
            onChange={value => onFilterChange('created_by', value)}
          />
        ),
      },
      {
        key: 'created_at' as const,
        component: (
          <CustomDatePickerWithRange
            controlId="created_at"
            placeholder="Created on"
            startDate={
              filters.created_at?.[0]
                ? new Date(filters.created_at?.[0])
                : undefined
            }
            endDate={
              filters.created_at?.[1]
                ? new Date(filters.created_at?.[1])
                : undefined
            }
            onChange={([start, end]) =>
              onFilterChange('created_at', [start ?? null, end ?? null])
            }
          />
        ),
      },
      {
        key: 'template_category' as const,
        component: hideCategories ? (
          <></>
        ) : (
          <DropdownTypeahead
            disabled
            hideLabel
            label="Template Category"
            placeholder="Template Category"
            options={[]}
            selected={filters.template_category}
            onChange={value =>
              onFilterChange('template_category', value as FilterOption)
            }
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            onInputChange={() => {}}
          />
        ),
      },
    ],
    [filters, onFilterChange, templateUsers],
  );

  return (
    <div className="ra-listing-filters">
      <Row className="equal-width-cols">
        {filterConfig.map(({key, component}) => (
          <Col key={key} className="col-equal">
            <Form.Group controlId={key}>{component}</Form.Group>
          </Col>
        ))}
        <Col className="col-equal">
          <Form.Group>
            <CategoriesFiltersDrawer
              filters={filters}
              onFilterChange={onFilterChange}
            />
          </Form.Group>
        </Col>
      </Row>
    </div>
  );
};
